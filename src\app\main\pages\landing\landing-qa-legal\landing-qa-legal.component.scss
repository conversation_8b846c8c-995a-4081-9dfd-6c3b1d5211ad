/* --- 1. <PERSON><PERSON><PERSON> (BLUE THEME) --- */
:host {
    --primary-blue: #0056b3;
    --accent-blue: #007bff;
    --light-blue-bg: #ebf5ff;
    --text-main: #333;
    --text-gray: #666;
    --bg-body: #f4f6f9;
    --border-color: #dee2e6;
    --warning-bg: #fffbe8;
    --warning-border: #f6e4b8;
    --answer-bg: #f3f8ff;
    --answer-border: #dce8fb;
}

/* --- 2. RESET & BASE --- */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

:host {
    display: block;
    background-color: var(--bg-body);
    color: var(--text-main);
    font-size: 14px;
    line-height: 1.5;
}

a {
    text-decoration: none;
    color: inherit;
    transition: 0.2s;
}

ul {
    list-style: none;
}

/* --- 3. NAVBAR --- */
.navbar {
    background: #fff;
    padding: 10px 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #dae1e7;
    position: sticky;
    top: 0;
    z-index: 100;
}

.nav-left {
    display: flex;
    align-items: center;
    gap: 40px;
}

.logo-box {
    font-size: 22px;
    display: flex;
    align-items: center;
    font-weight: bold;
}

.logo-c {
    color: #7f9eb2;
}

.logo-ai {
    color: #f1c40f;
}

.logo-cls {
    color: var(--accent-blue);
    font-size: 26px;
    font-weight: 800;
    border-left: 2px solid #ccc;
    padding-left: 12px;
    margin-left: 10px;
}

.nav-menu {
    display: flex;
    gap: 25px;
}

.nav-item {
    color: var(--text-gray);
    font-weight: 600;
    font-size: 15px;
    cursor: pointer;
    padding: 8px 0;
    border-bottom: 3px solid transparent;

    &:hover,
    &.active {
        color: var(--accent-blue);
        border-bottom-color: var(--accent-blue);
    }
}

.btn-trial {
    background-color: var(--light-blue-bg);
    color: var(--accent-blue);
    padding: 8px 20px;
    border-radius: 20px;
    font-weight: bold;
    border: none;
    cursor: pointer;

    &:hover {
        background-color: #d6eaff;
    }
}

/* --- 4. MAIN LAYOUT --- */
.container {
    max-width: 1200px;
    margin: 25px auto;
    display: flex;
    gap: 20px;
    padding: 0 15px;
}

/* SIDEBAR (Left) */
.sidebar {
    width: 260px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    height: fit-content;
}

.sb-header {
    background: #f8f9fa;
    padding: 12px 15px;
    font-weight: bold;
    font-size: 15px;
    color: #495057;
    border-bottom: 1px solid var(--border-color);
}

.sb-subheader {
    padding: 10px 15px;
    font-weight: 700;
    color: var(--primary-blue);
    border-bottom: 1px solid #f0f0f0;
    background: #fff;
}

.sb-list li {
    padding: 10px 15px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    color: #444;

    &:hover {
        background-color: #f8f9fa;
        color: var(--accent-blue);

        .sb-desc {
            display: block;
        }
    }

    &.active {
        background-color: var(--light-blue-bg);
        color: var(--primary-blue);
        font-weight: 600;
        border-left: 3px solid var(--primary-blue);
    }
}

.sb-desc {
    display: none;
    font-size: 12px;
    color: #888;
    margin-top: 3px;
    font-weight: normal;
    line-height: 1.3;
}

/* MAIN CONTENT (Right) */
.main-content {
    flex: 1;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    min-height: 600px;
}

.mc-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.mc-title {
    font-size: 16px;
    font-weight: 700;
    color: var(--primary-blue);
    text-transform: uppercase;
}

.search-wrap {
    position: relative;
}

.search-inp {
    padding: 6px 12px;
    padding-right: 30px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    width: 250px;
    font-size: 13px;
}

.search-icon {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    font-size: 12px;
    cursor: pointer;
}

/* Question List */
.qa-list {
    padding: 0 20px;
    flex: 1;
}

.no-results {
    padding: 20px;
    color: #666;
}

.qa-item {
    display: flex;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid #eee;
    cursor: pointer;

    &:hover {
        background-color: #fafafa;

        .qa-title {
            color: var(--accent-blue);
        }
    }
}

.qa-id {
    font-size: 12px;
    color: #999;
    min-width: 45px;
    margin-top: 3px;
}

.qa-body {
    flex: 1;
}

.qa-title {
    font-size: 15px;
    font-weight: 700;
    color: #333;
    margin-bottom: 5px;
}

.qa-meta {
    font-size: 12px;
    color: var(--accent-blue);
    font-style: italic;
}

.qa-date {
    color: #888;
    margin-left: 10px;
    font-style: normal;
}

/* Footer / Pagination */
.mc-footer {
    padding: 15px 20px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #fbfbfb;
    border-radius: 0 0 4px 4px;
}

.page-size-wrap {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    color: #555;
}

.page-select {
    padding: 4px 8px;
    border: 1px solid #ccc;
    border-radius: 3px;
    outline: none;
}

.pagination {
    display: flex;
    gap: 4px;
}

.pg-btn {
    min-width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #dee2e6;
    background: #fff;
    color: var(--primary-blue);
    cursor: pointer;
    border-radius: 3px;

    &.active {
        background: var(--primary-blue);
        color: #fff;
        border-color: var(--primary-blue);
    }

    &:hover:not(.active):not(:disabled) {
        background: #e9ecef;
    }

    &:disabled {
        cursor: not-allowed;
        opacity: 0.5;
    }
}

/* --- 5. MODAL (POPUP) --- */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    justify-content: center;
    align-items: center;

    &.show {
        display: flex;
    }
}

.modal-content {
    background-color: #fff;
    width: 800px;
    max-width: 90%;
    max-height: 90vh;
    border-radius: 6px;
    overflow-y: auto;
    position: relative;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }

    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.modal-close {
    position: absolute;
    right: 15px;
    top: 10px;
    font-size: 24px;
    color: #aaa;
    cursor: pointer;
    z-index: 10;

    &:hover {
        color: #000;
    }
}

.modal-body {
    padding: 30px;
}

/* Modal Content Styles */
.m-title {
    font-size: 20px;
    font-weight: 700;
    color: var(--primary-blue);
    margin-bottom: 20px;
    padding-right: 20px;
    line-height: 1.4;
}

.m-question-box {
    background: var(--warning-bg);
    border: 1px dashed var(--warning-border);
    padding: 20px;
    border-radius: 5px;
    margin-bottom: 25px;
}

.m-q-label {
    font-weight: bold;
    color: #856404;
    margin-bottom: 8px;
    display: block;
}

.m-q-text {
    font-style: italic;
    color: #333;
}

.m-answer-area h3 {
    color: var(--primary-blue);
    margin-bottom: 10px;
    font-size: 16px;
    text-transform: uppercase;
    border-bottom: 2px solid #eee;
    display: inline-block;
    padding-bottom: 4px;
}

.m-answer-box {
    background: var(--answer-bg);
    border: 1px solid var(--answer-border);
    padding: 16px;
    border-radius: 5px;
}

.m-a-text {
    line-height: 1.6;
    text-align: justify;
    color: #222;
}

::ng-deep .ref-link {
    color: var(--accent-blue);
    font-weight: 600;
    cursor: pointer;
}

/* --- LOADING STATE --- */
.loading {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-gray);
    font-style: italic;
}

.loading::after {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    margin-left: 10px;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--accent-blue);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.landing__to-top {
    position: fixed;
    right: min(24px, 4vw);
    bottom: min(24px, 4vh);
    z-index: 1100;

    width: 44px;
    height: 44px;
    border: 1px solid rgba(2, 18, 43, .10);
    border-radius: 999px;
    background: #0EA5E9;
    color: #fff;

    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    box-shadow: 0 8px 18px rgba(2, 18, 43, .18);
    transition: transform .15s ease, box-shadow .15s ease, opacity .2s ease;
}

.landing__to-top:hover {
    transform: translateY(-1px);
    box-shadow: 0 12px 26px rgba(2, 18, 43, .24);
}

.landing__to-top:active {
    transform: translateY(0);
}

.landing__to-top:focus-visible {
    outline: none;
    box-shadow: 0 0 0 4px color-mix(in srgb, #0EA5E9 22%, white);
}

@media (max-width: 576px) {
    .landing__to-top {
        width: 52px;
        height: 52px;
    }
}

/* Respect reduced-motion */
@media (prefers-reduced-motion: reduce) {
    .landing__to-top {
        transition: none;
    }
}

.container {
    max-width: 1400px;
    padding: 0
}