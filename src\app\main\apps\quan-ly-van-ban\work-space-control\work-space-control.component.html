<div class="modal-body z-1000" tabindex="0" ngbAutofocus>
  <form (ngSubmit)="submit()" [formGroup]="formWorkSpace" (keydown.enter)="onEnterKey($event)" >
    <div class="row">
      <div class="col-12">
        <div class="form-group">
          <label
            for="basicTextarea"
            class="w-100 align-items-center d-flex justify-content-between"
            >{{ title }}
            <div class="">
              <button
                type="button"
                class="btn btn-sm ml-auto p-0"
                (click)="modal.dismiss('Cross click')"
              >
                <img src="assets/images/icons/x.svg" alt="x" />
              </button></div
          ></label>
        </div>
      </div>
      <div class="col-xl-12">
        <div class="form-group">
          <label for="basicInput">Tên dự án</label>
          <input
            type="text"
            class="form-control"
            placeholder="Tên dự án"
            formControlName="name"
            appFormControlValidation
          />
        </div>
      </div>
    </div>
    <div class="w-100 justify-content-end d-flex">
      <button
        type="button"
        rippleEffect
        class="btn btn-secondary mr-1"
        (click)="cancel()"
        [disabled]="isSubmitting"
      >
        Huỷ
      </button>
      <button [disabled]="formWorkSpace.invalid || isSubmitting" type="submit" rippleEffect class="btn btn-primary-theme">
        <span *ngIf="isSubmitting" class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
        {{ isSubmitting ? 'Đang xử lý...' : 'Xác nhận' }}
      </button>
    </div>
  </form>
</div>
