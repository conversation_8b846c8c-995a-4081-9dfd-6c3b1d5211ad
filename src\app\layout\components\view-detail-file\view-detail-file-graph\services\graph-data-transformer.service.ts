import { Injectable } from '@angular/core';
import {
  GraphApiResponse,
  ApiNode,
  ApiRelationship,
  GraphNode,
  GraphLink,
  DocumentData,
} from '../types/graph.types';
import {
  getNodeIdFromApiNode,
  getVietnameseRelationshipLabel,
  safeArray,
  formatDate,
  formatRelationshipDescription,
} from '../helper/helper';
import {
  NODE_COLORS,
  NODE_SIZES,
  RELATIONSHIP_DIRECTIONS,
  RELATIONSHIP_TYPES,
  LOAI_VAN_BAN_COLORS,
} from '../constants/graph.constants';

export interface TransformResult {
  nodes: GraphNode[];
  links: GraphLink[];
  apiNodeMap: Map<string, ApiNode>;
  rootNodeId: string;
  rootDocumentData: DocumentData | null;
}

/**
 * Service for transforming API graph data to visualization format
 */
@Injectable({
  providedIn: 'root',
})
export class GraphDataTransformerService {
  /**
   * Transform API graph data to visualization format
   */
  transformGraphData(
    graphData: GraphApiResponse,
    hiddenNodeIds: Set<string>
  ): TransformResult {
    const transformedNodes: GraphNode[] = [];
    const transformedLinks: GraphLink[] = [];
    const nodeMap = new Map<string, GraphNode>();
    const apiNodeMap = new Map<string, ApiNode>();
    const linkIdCounter = { value: 0 };
    let rootNodeId = '';
    let rootDocumentData: DocumentData | null = null;

    // Get seed node IDs from API response (root nodes) - can be multiple
    const seedNodeIds = new Set(safeArray(graphData.seed_node_ids).map(id => String(id)));

    // API format: graphData.nodes and relationships array
    const apiNodes = safeArray(graphData.nodes);
    if (apiNodes.length > 0) {
      // Build nodes and apiNodeMap
      apiNodes.forEach((apiNode: ApiNode, index: number) => {
        const id = getNodeIdFromApiNode(apiNode);
        if (!id) return;
        // Keep original api node keyed by our internal id
        apiNodeMap.set(id, { ...apiNode });

        const isHidden = hiddenNodeIds.has(id);
        // Determine if this is a root node: check if ID matches any seed_node_ids
        // If seed_node_ids is empty, fallback to index 0 for backward compatibility
        const isRoot = seedNodeIds.size > 0 
          ? seedNodeIds.has(id)
          : index === 0;
        const node = this.createNodeFromApiNode(apiNode, isRoot);
        if (!isHidden && !nodeMap.has(node.id)) {
          transformedNodes.push(node);
          nodeMap.set(node.id, node);
        }

        // Set rootNodeId to the first root node found (for backward compatibility)
        // rootDocumentData will be set from the first visible root node
        if (isRoot && !rootNodeId) {
          rootNodeId = node.id;
          if (!isHidden) {
            rootDocumentData = this.createDocumentDataFromApiNode(apiNode);
          }
        }
      });

      // Build links from relationships array
      const relationships = safeArray(graphData.relationships);
      if (relationships.length > 0) {
        const parallelEdgeCount = new Map<string, number>();
        for (const rel of relationships) {
          const rawSourceId = String(rel.source_id);
          const rawTargetId = String(rel.target_id);
          const directionIsOutgoing =
            rel.huong === RELATIONSHIP_DIRECTIONS.OUTGOING;
          const sourceId = directionIsOutgoing ? rawSourceId : rawTargetId;
          const targetId = directionIsOutgoing ? rawTargetId : rawSourceId;

          if (hiddenNodeIds.has(sourceId) || hiddenNodeIds.has(targetId)) {
            continue;
          }

          // Ensure nodes exist in map (relationships may reference nodes not present in nodes list)
          this.ensureNodeExists(
            sourceId,
            nodeMap,
            transformedNodes,
            apiNodeMap,
            hiddenNodeIds
          );
          this.ensureNodeExists(
            targetId,
            nodeMap,
            transformedNodes,
            apiNodeMap,
            hiddenNodeIds
          );

          if (nodeMap.has(sourceId) && nodeMap.has(targetId)) {
            const key = `${sourceId}_${targetId}`;
            const idx = (parallelEdgeCount.get(key) || 0) + 1;
            parallelEdgeCount.set(key, idx);

            // Assign curveness to separate parallel edges: 1st: 0, then ±0.2, ±0.35, ...
            const curveness = this.calculateCurveness(idx);

            const isIndirect = rel.thuoc_tinh?.type === 'gian_tiep';
            transformedLinks.push({
              id: `link_${linkIdCounter.value++}`,
              source: sourceId,
              target: targetId,
              label: getVietnameseRelationshipLabel(rel.loai_moi_quan_he || ''),
              strength:
                typeof rel.strength === 'number' ? rel.strength : undefined,
              __curveness: curveness,
              __relationshipType: rel.loai_moi_quan_he || '',
              __isIndirect: isIndirect,
              __description: isIndirect ? formatRelationshipDescription(rel.thuoc_tinh?.description, true) : undefined,
              __rels: isIndirect && rel.thuoc_tinh?.rels ? rel.thuoc_tinh.rels : undefined,
            });
          }
        }
      }
    }

    return {
      nodes: transformedNodes,
      links: transformedLinks,
      apiNodeMap,
      rootNodeId,
      rootDocumentData,
    };
  }

  /**
   * Create a graph node from API node
   */
  createNodeFromApiNode(
    apiNode: ApiNode,
    isRoot: boolean = false
  ): GraphNode {
    const nodeId = getNodeIdFromApiNode(apiNode);
    let label = '';
    let color = NODE_COLORS.DEFAULT;

    if (apiNode.nhan_ui === 'Điều khoản') {
      const viTri = apiNode.thuoc_tinh?.vi_tri || apiNode.thuoc_tinh?.so_hieu || apiNode.id_ui || '';
      label = this.buildMultilineLabel(viTri);
    } else {
      label = apiNode.thuoc_tinh?.so_hieu || apiNode.id_ui || '';
    }

    // Color based on document type (loai_van_ban); fallback to default
    if (apiNode.nhan_ui === 'Văn bản') {
      const loai = apiNode.thuoc_tinh?.loai_van_ban || '';
      color = LOAI_VAN_BAN_COLORS[loai] || NODE_COLORS.DOCUMENT;
    } else if (apiNode.nhan_ui === 'Điều khoản') {
      color = NODE_COLORS.ARTICLE;
    }

    return {
      id: nodeId,
      label: label,
      color: color,
    };
  }

  private buildMultilineLabel(
    rawLabel: string,
    maxWordsPerLine: number = 3,
    maxLines: number = 4
  ): string {
    const safeLabel = (rawLabel || '').trim();
    if (!safeLabel) {
      return '';
    }

    const words = safeLabel.split(/\s+/);
    const lines: string[] = [];

    for (let i = 0; i < words.length && lines.length < maxLines; i += maxWordsPerLine) {
      lines.push(words.slice(i, i + maxWordsPerLine).join(' '));
    }

    return lines.join('\n');
  }

  /**
   * Create document data from API node
   */
  createDocumentDataFromApiNode(apiNode: ApiNode): DocumentData {
    const thuocTinh = apiNode.thuoc_tinh || {};

    return {
      ten_day_du: thuocTinh.ten_day_du || apiNode.ten_day_du || '',
      so_hieu: thuocTinh.so_hieu || '',
      ngay_ban_hanh: formatDate(thuocTinh.ngay_ban_hanh || ''),
      loai_van_ban: thuocTinh.loai_van_ban || '',
      ngay_co_hieu_luc: formatDate(thuocTinh.ngay_co_hieu_luc || ''),
      ngay_dang_cong_bao: formatDate(thuocTinh.ngay_dang_cong_bao || ''),
      co_quan_ban_hanh: thuocTinh.co_quan_ban_hanh || '',
      chuc_danh: thuocTinh.chuc_danh || '',
      nguoi_ky: thuocTinh.nguoi_ky || '',
      pham_vi: thuocTinh.pham_vi || '',
      trich_yeu: thuocTinh.trich_yeu || '',
      tinh_trang_hieu_luc: thuocTinh.tinh_trang_hieu_luc || '',
      thoi_gian_cap_nhat: thuocTinh.thoi_gian_cap_nhat || '',
    };
  }

  /**
   * Ensure a node exists in the node map, creating a placeholder if needed
   */
  private ensureNodeExists(
    nodeId: string,
    nodeMap: Map<string, GraphNode>,
    transformedNodes: GraphNode[],
    apiNodeMap: Map<string, ApiNode>,
    hiddenNodeIds: Set<string>
  ): void {
    if (!nodeMap.has(nodeId) && !hiddenNodeIds.has(nodeId)) {
      const placeholderNode: ApiNode = {
        nhan_ui: 'Văn bản',
        id_ui: nodeId,
        thuoc_tinh: { ID: nodeId },
      };
      const placeholder = this.createNodeFromApiNode(placeholderNode, false);
      transformedNodes.push(placeholder);
      nodeMap.set(nodeId, placeholder);
      apiNodeMap.set(nodeId, placeholderNode);
    }
  }

  /**
   * Calculate curveness for parallel edges
   * 1st edge: 0, then ±0.4, ±0.6, ...
   */
  calculateCurveness(index: number): number {
    if (index === 1) return 0;

    const k = index - 1;
    // Increased base values for more spacing between parallel edges
    // Alternate between larger and smaller offsets for better distribution
    const base = k % 2 === 1 ? 0.4 : 0.6; // Increased from 0.2/0.35 to 0.4/0.6
    const sign = k % 4 < 2 ? 1 : -1; // alternate sides every two
    return sign * base;
  }
}

