:host {
  --primary-landing-color: #008fe3;
  --secondary-landing-color: #9e9e9e;
  --brand-accent: #0EA5E9;
  --section-gap: 3vh;
  --section-width: 80%;
  --footer-fluid: clamp(1280px, 92vw, 1600px);
  --footer-pad: 24px;
  --section-max: 1400px;
  --section-width: 92vw;
  --workspace-w: 72vw;
  --workspace-max: 1100px;
  --workspace-pad: 10px;
}

.landing__footer-link--accent {
  color: var(--brand-accent) !important;
  font-weight: 600;
}

.landing {
  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    height: 80px;
    padding: 16px 32px;
    position: sticky;
    top: 0;
    z-index: 1000;
    left: 0;
    right: 0;
    background-color: transparent;
    transition: background-color 0.2s ease, box-shadow 0.2s ease;

    &.is-scrolled {
      position: fixed;
      left: 0;
      right: 0;
      background-color: #ffffff;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    }

    // ---------- Logo section ----------
    &-logo {
      display: flex;
      align-items: center;
      gap: 16px;

      &-separator {
        height: 32px;
        border-left: 1px solid #e0e0e0;
      }

      &-text {
        font-size: 24px;
        font-weight: 700;
        color: var(--primary-landing-color);
      }
    }

    // ---------- Button section ----------
    &-action {
      text-decoration: none;

      &>.landing__header-login-btn {
        background-color: color-mix(in srgb,
            var(--primary-landing-color) 10%,
            white);
        color: var(--primary-landing-color);
        font-weight: 600;
        cursor: pointer;

        padding: 16px 32px;
        border-radius: 41px;
        border: none;
        transition: all 0.3s ease;

        &:hover {
          background-color: var(--primary-landing-color);
          color: #fff;
        }
      }
    }
  }

  /* ============================
   Legal Database – Responsive
   ============================ */
  .landing__database-stats {
    display: grid;
    grid-template-columns: repeat(12, minmax(0, 1fr));
    gap: 10px;
    align-items: stretch;

    /* chiều cao thẻ & chữ co giãn theo viewport */
    --card-min-h: 138px;
    --n-big: clamp(30px, 4.2vw, 46px);
    --n-mid: clamp(22px, 3.2vw, 36px);
    --n-sm: clamp(18px, 2.4vw, 28px);
    --label: clamp(11px, 1.5vw, 13px);

    .landing__database-stat-card {
      min-height: var(--card-min-h);
      border-radius: 10px;
      padding: 16px;
      color: #fff;
      display: flex;
      flex-direction: column;
      justify-content: center;
      text-align: left;
      transition: transform .2s ease, box-shadow .2s ease;
      will-change: transform;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 18px rgba(0, 0, 0, .08);
      }

      .landing__database-stat-number {
        font-weight: 700;
        line-height: 1.05;
        letter-spacing: .3px;
        white-space: nowrap;
      }

      .landing__database-stat-label {
        margin-top: 4px;
        font-weight: 600;
        opacity: .95;
        line-height: 1.25;
        font-size: var(--label);
      }

      &--blue-800 {
        background: #204887;
      }

      &--blue-700 {
        background: #2a5caf;
      }

      &--blue-600 {
        background: #3676e0;
      }

      &--blue-500,
      &--blue-base {
        background: #3b82f6;
      }
    }

    /* ===== Desktop ≥1200: bố cục như ảnh ===== */
    /* 1) 230K+ – chiếm 6 cột x 2 hàng (trái) */
    .landing__database-stat-card:nth-child(1) {
      grid-column: 1 / span 6;
      grid-row: 1 / span 2;

      .landing__database-stat-number {
        font-size: var(--n-big);
      }
    }

    /* 2) 77K+ – hàng 1, cột 7..10 */
    .landing__database-stat-card:nth-child(2) {
      grid-column: 7 / span 4;
      grid-row: 1;

      .landing__database-stat-number {
        font-size: var(--n-mid);
      }
    }

    /* 3) 14K+ – hàng 1, cột 11 */
    .landing__database-stat-card:nth-child(3) {
      grid-column: 11 / span 1;
      grid-row: 1;

      .landing__database-stat-number {
        font-size: var(--n-sm);
      }
    }

    /* 4) 40K+ – hàng 1, cột 12 */
    .landing__database-stat-card:nth-child(4) {
      grid-column: 12 / span 1;
      grid-row: 1;

      .landing__database-stat-number {
        font-size: var(--n-sm);
      }
    }

    /* 5) 42K+ – hàng 2, cột 7..8 */
    .landing__database-stat-card:nth-child(5) {
      grid-column: 7 / span 2;
      grid-row: 2;

      .landing__database-stat-number {
        font-size: var(--n-mid);
      }
    }

    /* 6) 29K+ – hàng 2, cột 9..11 */
    .landing__database-stat-card:nth-child(6) {
      grid-column: 9 / span 3;
      grid-row: 2;

      .landing__database-stat-number {
        font-size: var(--n-sm);
      }
    }

    /* 7) 18K+ – hàng 2, cột 12 */
    .landing__database-stat-card:nth-child(7) {
      grid-column: 12 / span 1;
      grid-row: 2;

      .landing__database-stat-number {
        font-size: var(--n-sm);
      }
    }

    /* ===== Laptop 992–1199: nén còn 8 cột===== */
    @media (max-width: 1199.98px) {
      grid-template-columns: repeat(8, minmax(0, 1fr));
      --card-min-h: 120px;

      .landing__database-stat-card:nth-child(1) {
        grid-column: 1 / span 4;
        grid-row: 1 / span 2;
      }

      .landing__database-stat-card:nth-child(2) {
        grid-column: 5 / span 3;
        grid-row: 1;
      }

      .landing__database-stat-card:nth-child(3) {
        grid-column: 8 / span 1;
        grid-row: 1;
      }

      .landing__database-stat-card:nth-child(5) {
        grid-column: 5 / span 2;
        grid-row: 2;
      }

      .landing__database-stat-card:nth-child(6) {
        grid-column: 7 / span 2;
        grid-row: 2;
      }

      .landing__database-stat-card:nth-child(4) {
        grid-column: 8 / span 1;
        grid-row: 2;
      }

      .landing__database-stat-card:nth-child(7) {
        grid-column: 8 / span 1;
        grid-row: 3;
      }
    }

    /* ===== Tablet 768–991: 6 cột ===== */
    @media (max-width: 991.98px) {
      grid-template-columns: repeat(6, minmax(0, 1fr));
      --card-min-h: 115px;

      .landing__database-stat-card:nth-child(1) {
        grid-column: 1 / span 3;
        grid-row: 1 / span 2;
      }

      .landing__database-stat-card:nth-child(2) {
        grid-column: 4 / span 3;
        grid-row: 1;
      }

      .landing__database-stat-card:nth-child(5) {
        grid-column: 4 / span 2;
        grid-row: 2;
      }

      .landing__database-stat-card:nth-child(6) {
        grid-column: 6 / span 1;
        grid-row: 2;
      }

      .landing__database-stat-card:nth-child(3) {
        grid-column: 1 / span 2;
        grid-row: 3;
      }

      .landing__database-stat-card:nth-child(4) {
        grid-column: 3 / span 2;
        grid-row: 3;
      }

      .landing__database-stat-card:nth-child(7) {
        grid-column: 5 / span 2;
        grid-row: 3;
      }
    }

    /* ===== Mobile ≤767: 2 cột (khối lớn full-width) ===== */
    @media (max-width: 767.98px) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
      gap: 8px;
      --card-min-h: 108px;

      .landing__database-stat-card:nth-child(1) {
        grid-column: 1 / -1;
        grid-row: auto;
      }

      .landing__database-stat-card:nth-child(2) {
        grid-column: 1 / -1;
      }

      .landing__database-stat-card:nth-child(5) {
        grid-column: 1 / -1;
      }

      /* các ô còn lại xếp 2-cột */
      .landing__database-stat-card:nth-child(3) {
        grid-column: 1;
      }

      .landing__database-stat-card:nth-child(4) {
        grid-column: 2;
      }

      .landing__database-stat-card:nth-child(6) {
        grid-column: 1;
      }

      .landing__database-stat-card:nth-child(7) {
        grid-column: 2;
      }

      /* chữ nhỏ hơn chút để tránh wrap */
      --n-big: clamp(26px, 7vw, 34px);
      --n-mid: clamp(20px, 5.6vw, 28px);
      --n-sm : clamp(18px, 4.8vw, 24px);
      --label: clamp(11px, 3.5vw, 12px);
    }
  }

  // .landing__chatbot {
  //   padding: 70px 0 120px;
  // }

  // ===============================
  // Main Section
  // ===============================
  &__main {
    display: grid;
    grid-template-columns: 200px 0.9fr;
    min-height: calc(100vh - 80px);

    &-nav {
      display: flex;
      flex-direction: column;
      // justify-content: center;
      margin-top: 80px;

      &-list {
        list-style: none;
        display: flex;
        flex-direction: column;
        gap: 28px;
      }

      &-item {
        font-weight: 600;
        color: var(--secondary-landing-color);
        cursor: pointer;
        transition: background-color 0.2s ease;

        &:hover {
          color: var(--primary-landing-color);
        }
      }
    }
  }

  // ---------- Content Wrapper ----------
  &__main-content {
    display: flex;
    flex-direction: column;
    align-items: center;

    gap: var(--section-gap);
  }

  // ---------- Each Section ----------
  &__main-section {
    width: var(--section-width);
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
  }

  // ===================================
  // Information Section (formerly Intro)
  // ===================================
  &__information {
    display: flex;
    flex-direction: column;
    align-items: center;

    gap: var(--section-gap);
  }

  // Intro block (children inside Information section)
  &__intro {
    &-wrapper {
      display: grid;
      grid-template-columns: 1fr 1fr;
      align-items: center;
      gap: 48px;
    }

    &-content {
      display: flex;
      flex-direction: column;
      gap: 14px;
    }

    &-title {
      margin: 0;
      font-size: clamp(40px, 3.5vw, 52px);
      font-weight: 700;
      line-height: 1.05;

      &-highlight {
        color: var(--primary-landing-color);
      }
    }

    &-subtitle {
      margin: 0;
      font-size: clamp(30px, 2.7vw, 40px);
      font-weight: 700;
      line-height: 1.2;
      color: #111827;
    }

    &-description {
      font-size: clamp(18px, 1.5vw, 20px);
      line-height: 1.7;
      color: rgba(0, 0, 0, 0.75);
      max-width: 480px;
    }

    @media (max-width: 768px) {
      .landing__intro-title {
        font-size: 34px;
      }

      .landing__intro-subtitle {
        font-size: 26px;
      }

      .landing__intro-description {
        font-size: 16px;
      }
    }

    &-illustration {
      display: flex;
      justify-content: center;

      &>.landing__intro-image {
        width: 100%;
        max-width: 620px;
        height: auto;
        object-fit: contain;
      }
    }

    &-button {
      max-width: 160px;
      background-color: var(--primary-landing-color);
      color: #fff;
      font-weight: 600;
      padding: 12px 24px;
      border: none;
      border-radius: 999px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background-color: color-mix(in srgb,
            var(--primary-landing-color) 80%,
            black);
      }
    }
  }

  // ===================================
  // Workspace Section
  // ===================================
  &__workspace {
    &-header {
      margin-bottom: 32px;

      &>.landing__workspace-title {
        text-align: center;
        font-size: clamp(30px, 2.6vw, 36px);
        font-weight: 700;
        color: #212121;
        margin-bottom: 12px;
      }

      &>.landing__workspace-description {
        text-align: center;
        color: var(--secondary-landing-color);
        font-size: clamp(15px, 1.4vw, 18px);
        line-height: 1.7;
        max-width: clamp(440px, 38vw, 560px);
        margin: 0 auto;
        margin-bottom: 32px;
      }

      &-cards {
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        gap: 24px;
      }
    }
  }

  // ===================================
  // Legal Database Section
  // ===================================
  &__database {
    &-wrapper {
      text-align: center;
      width: 100%;
    }

    &-header {
      margin-bottom: 32px;

      &>.landing__database-title {
        font-size: clamp(30px, 2.6vw, 36px);
        font-weight: 700;
        color: #212121;
        margin-bottom: 12px;
      }

      &>.landing__database-description {
        color: var(--secondary-landing-color);
        font-size: clamp(15px, 1.4vw, 18px);
        line-height: 1.7;
        max-width: 600px;
        margin: 0 auto;
        margin-bottom: 32px;
      }
    }

    &-stats {
      display: grid;
      gap: 8px;
      grid-template-areas:
        "main main small1 small2 small3"
        "main main small4 small5 small6";
      grid-template-columns: 1fr 1fr 1.5fr 1fr 1fr;
      margin: 0 auto;
      padding: 0 16px;

      .landing__database-stat-card {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        text-align: left;
        border-radius: 8px;
        padding: 16px;
        color: #fff;
        transition: transform 0.2s ease;

        &:hover {
          transform: translateY(-2px);
        }

        .landing__database-stat-number {
          font-size: 30px;
          font-weight: 700;
          margin-bottom: 4px;
          color: #fff;
        }

        .landing__database-stat-label {
          font-size: 12px;
          font-weight: 500;
          color: #fff;
          opacity: 0.9;
        }

        &:nth-child(1) .landing__database-stat-number {
          font-size: 44px;
        }

        &:nth-child(2) .landing__database-stat-number,
        &:nth-child(5) .landing__database-stat-number {
          font-size: 34px;
        }

        &:nth-child(3) .landing__database-stat-number,
        &:nth-child(4) .landing__database-stat-number,
        &:nth-child(6) .landing__database-stat-number,
        &:nth-child(7) .landing__database-stat-number {
          font-size: 26px;
        }

        &:nth-child(1) {
          grid-area: main;
        }

        &:nth-child(2) {
          grid-area: small1;
          grid-column: span 1.2;
        }

        &:nth-child(3) {
          grid-area: small2;
        }

        &:nth-child(4) {
          grid-area: small3;
        }

        &:nth-child(5) {
          grid-area: small4;
        }

        &:nth-child(6) {
          grid-area: small5;
        }

        &:nth-child(7) {
          grid-area: small6;
        }

        &--blue-800 {
          background-color: #204887;
        }

        &--blue-700 {
          background-color: #2a5caf;
        }

        &--blue-600 {
          background-color: #3676e0;
        }

        &--blue-500 {
          background-color: #3b82f6;
        }

        &--blue-base {
          background-color: #3b82f6;
        }
      }
    }
  }

  // ===================================
  // Mission Section
  // ===================================
  &__mission {
    &-figure {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    &-image {
      max-width: 100%;
      height: auto;
      object-fit: contain;
    }
  }

  // ===================================
  // Articles Section
  // ===================================
  &__articles {
    &-header {
      margin-bottom: 32px;

      &>.landing__articles-title {
        text-align: center;
        font-size: clamp(30px, 2.6vw, 32px);
        font-weight: 700;
        color: #212121;
        margin-bottom: 48px;
      }
    }
  }

  #articles.landing__main-section {
    padding-top: 2px !important;
    padding-bottom: 0 !important;
  }

  // Separator between sections
  &__section-separator {
    width: var(--section-width);
    max-width: 1200px;
    margin-top: 100px;
    /* space above and below */
    height: 1px;
    background: #e0e0e0;
  }

  // ===================================
  // Contact Section
  // ===================================
  &__contact {
    &-wrapper {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 48px;
      align-items: start;
    }

    &-info {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    &-title {
      font-size: 26px;
      font-weight: 700;
      color: #212121;
      margin: 0;
    }

    &-description {
      width: 360px;
      line-height: 1.6;
      margin: 0;
    }


    &-title {
      font-size: clamp(30px, 2.6vw, 36px);
      font-weight: 700;
      color: #212121;
      margin: 0;
    }

    &-description {
      font-size: clamp(15px, 1.4vw, 18px);
      line-height: 1.7;
      width: auto;
      max-width: clamp(420px, 40vw, 540px);
      margin: 0;
    }

    &-list {
      display: flex;
      flex-direction: column;
      gap: 16px;
      margin-top: 12px;
    }

    &-item {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    &-icon {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      background-color: var(--primary-landing-color);
      display: inline-flex;
      align-items: center;
      justify-content: center;
      color: white;

      i[data-feather] {
        width: 18px;
        height: 18px;
        color: var(--primary-landing-color);
      }
    }

    &-text {
      color: #0b1526;
    }

    &-form-el {
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    &-label {
      display: flex;
      flex-direction: column;
      gap: 6px;
      font-size: 12px;
      color: #616161;
    }

    &-input,
    &-textarea {
      width: 100%;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      padding: 10px 12px;
      outline: none;
      font-size: 14px;
      transition: border-color 0.2s ease, box-shadow 0.2s ease;

      &:focus {
        border-color: var(--primary-landing-color);
        box-shadow: 0 0 0 3px color-mix(in srgb, var(--primary-landing-color) 15%, white);
      }
    }

    &-textarea {
      grid-column: 1 / -1;
      min-height: 140px;
      resize: none;
    }

    &-submit {
      grid-column: 1 / -1;
      background-color: var(--primary-landing-color);
      color: #fff;
      border: none;
      border-radius: 999px;
      padding: 12px 20px;
      font-weight: 600;
      cursor: pointer;
    }
  }

  &__header {
    &-nav {
      &-item {
        display: inline-flex;
        align-items: center;
        box-sizing: border-box;

        font-size: 16px;
        line-height: 1;
        padding-inline: 20px;
        padding-block: 0;
        white-space: nowrap;
        margin: 0;

        cursor: pointer;
        font-weight: 600;
        color: var(--secondary-landing-color);
        position: relative;
        transition: color 0.2s ease;

        &:hover {
          color: var(--primary-landing-color);
        }

        &.active {
          color: var(--primary-landing-color);

          &::after {
            content: "";
            position: absolute;
            left: 0;
            right: 0;
            bottom: -10px;
            height: 2px;
            border-radius: 2px;
            background-color: var(--primary-landing-color);
          }
        }
      }
    }
  }
}

.landing__intro-description {
  color: #000000b2;
  color: rgba(0, 0, 0, 0.70);
}

.landing .landing__workspace-wrapper .landing__workspace-description {
  color: #000000b2 !important;
}

.landing .landing__database-wrapper .landing__database-description {
  color: #000000b2 !important;
}

.landing__workspace-description,
.landing__database-description {
  line-height: 1.6;
}

.landing h1,
.landing h2,
.landing h3,
.landing p {
  margin-block-start: 0;
}

.landing figure {
  margin: 0;
}

.landing__main-section {
  width: var(--section-width);
  max-width: var(--section-max);
  padding-block: 32px 12px;
  scroll-margin-top: calc(var(--header-h) + 8px);
}

.landing__intro-wrapper {
  gap: 32px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  align-items: center;
  column-gap: 48px;
  grid-template-areas: "content illus";
  width: 100%;
  max-width: var(--section-max);
  margin-inline: auto;

  @media (min-width: 1400px) {
    gap: 40px;
  }
}

.landing__intro-content {
  grid-area: content;
}

.landing__intro-illustration {
  width: 100%;
  display: flex;
  justify-content: flex-end;
}



@media (max-width: 992px) {
  .landing__intro-wrapper {
    grid-template-columns: 1fr;
    row-gap: 24px;
    column-gap: 0;
    grid-template-areas:
      "illus"
      "content";
    padding: 0 16px;
    max-width: 100%;
  }

  .landing__intro-content {
    grid-area: content;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    padding: 0 12px;
  }

  .landing__intro-illustration {
    grid-area: illus;
    justify-content: center;
    padding: 0 20px;

    .landing__intro-image {
      max-width: 100%;
      width: auto;
      height: auto;
    }
  }

  .landing__intro-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: min(100%, 340px);
    padding: 14px 22px;
    border-radius: 999px;
    margin-top: 8px;
    font-size: 16px;
    font-weight: 600;
  }
}

/* Tablet nhỏ */
@media (max-width: 768px) {
  .landing__intro-wrapper {
    row-gap: 20px;
    padding: 0 12px;
  }

  .landing__intro-content {
    gap: 14px;
    padding: 0 8px;
  }

  .landing__intro-illustration {
    padding: 0 16px;
  }
}

@media (max-width: 576px) {
  .landing__intro-wrapper {
    row-gap: 16px;
    padding: 0 8px;
  }

  .landing__intro-content {
    gap: 12px;
    padding: 0;
  }

  .landing__intro-illustration {
    padding: 0 12px;
  }

  .landing__intro-button {
    width: 100%;
    padding: 16px 24px;
    font-size: 15px;
    margin-top: 6px;
  }
}

// .landing__information,
.landing__mission,
.landing__articles,
.landing__contact {
  padding: 24px 0 48px;
}

.landing__information {
  padding-top: 0 !important;
}

.landing__articles-wrapper {
  margin-top: 0;
}

// .landing__articles-title{ margin: 0 0 12px; }

// .landing__section-separator{ margin: 20px 0 20px; }

.landing__footer {
  background: #0f3656;
  color: #dbe7f5;
  padding: 70px 0 50px;
  border-top: 1px solid rgba(255, 255, 255, 0.06);
  margin-left: calc(50% - 50vw);
  margin-right: calc(50% - 50vw);
  width: 100vw;
}

.landing__footer-container {
  max-width: 1580px;
  margin: 0 auto;
  padding: 0 16px;

  display: grid;
  grid-template-columns: 1.35fr 1fr;
  grid-template-rows: auto auto;
  grid-template-areas:
    "info map"
    "news map";
  align-items: start;
  gap: 28px;
}

.landing__footer-info {
  grid-area: info;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.landing__footer-map {
  grid-area: map;
  min-height: 320px;
  align-self: stretch;
}

.landing__footer-newsletter {
  grid-area: news;
  align-self: end;
}

.landing__footer-map-frame {
  width: 100%;
  height: 100%;
  min-height: 320px;
  border: 0;
  border-radius: 12px;
  box-shadow: 0 4px 14px rgba(0, 0, 0, 0.18);
}

.landing__intro-button {
  min-width: 200px;
  white-space: nowrap;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.landing__footer-brand {
  display: flex;
  align-items: center;
  gap: 10px;
}

.landing__footer-logo {
  width: 36px;
  height: 26px;
  display: block;
}

.landing__footer-brand-text {
  font-weight: 700;
  font-size: 18px;
  letter-spacing: 0.3px;
  color: #0EA5E9;
}

.landing__footer-tagline {
  max-width: 720px;
  line-height: 1.55;
  margin: 0;
}

.tagline-br-desktop {
  display: inline;
}

@media (min-width: 992px) {
  .tagline-br-desktop {
    display: block;
  }
}

@media (max-width: 576px) {
  .landing__footer-row {
    grid-template-columns: 1fr;
  }
}

.landing__footer-list {
  list-style: none;
  padding: 0;
  margin: 6px 0 0 0;
}

.landing__footer-list li {
  margin: 4px 0;
  color: #e7f0fb;
  font-size: 14px;
}

.landing__footer-label {
  color: #a7c0e8;
  font-weight: 600;
  display: inline-block;
  width: 90px;
}

.landing__footer-contact-row .landing__footer-label {
  visibility: visible;
}

.landing__footer-row {
  display: grid;
  grid-template-columns: 90px 1fr;
  column-gap: 16px;
  align-items: start;
}

.landing__footer-value a {
  color: #cfe0f7;
  text-decoration: none;
}

.landing__footer-value a:hover {
  text-decoration: underline;
}

.landing__footer-label {
  width: auto;
}

.landing__footer-newsletter {
  background: #ffffff;
  border: 1px solid #e6ecf5;
  border-radius: 14px;
  padding: 36px;
  display: grid;
  grid-template-columns: 1.2fr minmax(220px, 0.7fr) auto;
  align-items: center;
  gap: 12px;
  max-width: 880px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
}

.landing__footer-newsletter-text {
  color: #1f2a37;
  font-size: 13.5px;
  line-height: 1.5;
  margin: 0;
}

.landing__footer-input {
  width: 100%;
  border: 0;
  outline: none;
  height: 42px;
  padding: 0 12px;
  border-radius: 8px;
  background: #f3f5f8;
}

.landing__footer-button {
  height: 42px;
  padding: 0 16px;
  border: 0;
  border-radius: 10px;
  background: #2f6fb5;
  color: #fff;
  font-weight: 600;
  cursor: pointer;
  min-width: 108px;
}

.landing__footer-button:hover {
  filter: brightness(1.05);
}

/* Bottom bar */
.landing__footer-bottom {
  max-width: 1580px;
  margin: 20px auto 0;
  padding: 12px 16px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.08);
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #b8cdea;
  font-size: 13px;
}

.landing__footer-bottom-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.landing__footer-bottom-label {
  color: #cfe0f7;
  font-weight: 600;
}

.landing__footer-link {
  color: #cfe0f7;
  text-decoration: none;
}

.landing__footer-link:hover {
  text-decoration: underline;
}

.landing__footer-sep {
  opacity: 0.7;
}

:host {
  --workspace-w: 72vw;
  --workspace-max: 1100px;
  --workspace-pad: 12px;
  --sidebar-max: 200px;
  --content-max: 1200px;
}

.landing__main {
  display: grid;
  grid-template-columns: 1fr !important;
  min-width: 0;
  box-sizing: border-box;
}

.landing__main-content {
  width: var(--section-width);
  max-width: var(--section-max);
  margin-inline: auto;
}

.landing__main-section {
  width: var(--section-width);
  max-width: var(--section-max);
  margin-inline: auto;
  padding-left: 0;
  padding-right: 0;
}

.landing__workspace-wrapper {
  width: var(--workspace-w);
  max-width: var(--workspace-max);
  margin: 0 auto;
  padding-inline: var(--workspace-pad);
}

.landing__workspace-cards {
  max-width: 100%;
  margin: 0 auto;
}

/* ===================== */
/* Header: burger & drawer */
/* ===================== */
.landing__burger {
  display: none;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
  width: 40px;
  height: 40px;
  padding: 8px;
  margin-right: 10px;
  border: 1px solid #e6ecf5;
  background: rgba(14, 165, 233, 0.06);
  border-radius: 10px;
  cursor: pointer;
}

.landing__burger span {
  width: 22px;
  height: 3px;
  border-radius: 2px;
  background: var(--primary-landing-color);
  display: block;
  transition: transform .2s ease, opacity .2s ease;
}

.landing__burger:hover {
  filter: brightness(1.03);
}

.landing__drawer-backdrop {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.35);
  z-index: 999;
}

.landing__drawer {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  width: min(84vw, 320px);
  height: 100vh;
  background: #fff;
  box-shadow: 2px 0 16px rgba(0, 0, 0, 0.12);
  transform: translateX(-100%);
  transition: transform .22s ease-out;
  display: flex;
  flex-direction: column;

  &.open {
    transform: translateX(0);
  }

  &-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 18px;
    border-bottom: 1px solid #eee;
  }

  &-title {
    font-weight: 700;
    color: #0b1526;
  }

  &-close {
    border: 0;
    background: transparent;
    font-size: 20px;
    line-height: 1;
    cursor: pointer;
  }

  &-nav {
    padding: 12px 8px;
  }

  &-list {
    list-style: none;
    margin: 0;
    padding: 0;
  }

  &-item {
    padding: 12px 14px;
    border-radius: 10px;
    cursor: pointer;
    font-weight: 600;
    color: #1f2937;

    &:hover {
      background: #f3f5f8;
      color: var(--primary-landing-color);
    }
  }

  &-login {
    margin-top: 6px;
    color: #fff;
    background: var(--primary-landing-color);
    text-align: center;
  }
}

.landing__header-nav-list {
  display: flex;
  align-items: center;
  gap: 0;
}

/* ===================== */
/* Chatbot wrapper*/
/* ===================== */
:root {
  --chatbot-min: 520px;
  --chatbot-fluid: 68vw;
  --chatbot-max: 920px;
}

// .landing__chatbot{
//   padding: clamp(36px, 8vh, 64px) 0 40px;
// }
.landing__chatbot app-landing-chatbot {
  display: block;
  width: clamp(var(--chatbot-min), var(--chatbot-fluid), var(--chatbot-max));
  margin: 0 auto;
}

#chatbot.landing__main-section {
  padding-inline: 0;
}

#chatbot.landing__main-section {
  padding-inline: 0;
}

/* Hiệu ứng gradient wave cho tiêu đề chatbot */
#chatbot .chatbot__title {
  position: relative;
  display: inline-block;
  background: linear-gradient(90deg,
      #2384C8 0%,
      #D1D0D5 50%,
      #F8D018 100%);
  background-size: 300% 100%;
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  -webkit-text-fill-color: transparent;
  animation: title-shine 10s ease-in-out infinite;
  will-change: background-position;
  text-shadow: none;
}

@keyframes title-shine {
  0% {
    background-position: 0% 0;
  }

  50% {
    background-position: 100% 0;
  }

  100% {
    background-position: 0% 0;
  }
}

@media (prefers-reduced-motion: reduce) {
  #chatbot .chatbot__title {
    animation: none;
    background: none;
    -webkit-text-fill-color: #111;
    color: #111;
  }
}

/* ===================== */
/* Responsive layout */
/* ===================== */

@media (max-width: 1200px) {
  :host {
    --section-width: 94vw;
  }
}


@media (prefers-reduced-motion: reduce) {
  .chatbot__title {
    animation: none;
    background: none;
    -webkit-text-fill-color: #111 !important;
    color: #111 !important;
  }
}

@keyframes title-shine {
  0% {
    background-position: 200% 0;
  }

  100% {
    background-position: 0% 0;
  }
}

/* ===================== */
/* Responsive layout */
/* ===================== */

@media (max-width: 1200px) {
  :host {
    --section-width: 94vw;
  }
}

@media (max-width: 992px) {
  .landing__header {
    padding: 12px 16px;
  }

  .landing__burger {
    display: inline-flex;
  }

  .landing__header-nav,
  .landing__header-action {
    display: none !important;
  }

  .landing__main {
    display: block !important;
  }

  .landing__main-section {
    width: 94vw !important;
    max-width: var(--section-max);
    padding-left: 0 !important;
    padding-right: 0 !important;
  }

  :root {
    --chatbot-min: 460px;
    --chatbot-fluid: 84vw;
    --chatbot-max: 760px;
  }
}

/* ≤768: full theo section */
@media (max-width: 768px) {
  :root {
    --chatbot-min: 320px;
    --chatbot-fluid: 94vw;
    --chatbot-max: 680px;
  }
}

/* Footer tweaks */
@media (max-width: 992px) {
  .landing__footer-container {
    grid-template-columns: 1fr;
    grid-template-areas: "info" "map" "news";
  }

  .landing__footer-map,
  .landing__footer-map-frame {
    min-height: 260px;
  }

  .landing__footer-newsletter {
    grid-template-columns: 1fr;
    gap: 10px;
    max-width: 100%;
    padding: 16px;
  }

  .landing__footer-button {
    width: 100%;
  }
}

@media (max-width: 576px) {
  .landing__footer {
    padding: 48px 0 32px;
  }

  .landing__footer-brand-text {
    font-size: 17px;
  }

  .landing__footer-tagline,
  .landing__footer-list li {
    font-size: 13.5px;
  }
}

@media (max-width: 576px) {
  .landing__database-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    /* 4 cột đều nhau */
    gap: 10px;

    /* Chiều cao & chữ gọn lại để không tràn */
    --card-min-h: 106px;

    .landing__database-stat-card {
      min-height: var(--card-min-h);
      padding: 14px;
    }

    .landing__database-stat-card .landing__database-stat-number {
      font-size: clamp(22px, 6vw, 30px);
      line-height: 1.1;
      white-space: nowrap;
    }

    .landing__database-stat-card .landing__database-stat-label {
      font-size: clamp(11px, 3.5vw, 12px);
    }

    /* Dòng 1: 230K+ (item 1) full width */
    .landing__database-stat-card:nth-child(1) {
      grid-column: 1 / -1;
      /* span 4 cột */
      grid-row: 1;
    }

    /* Dòng 2: 77K (item 2), 29K (item 5), 14K (item 3), 42K (item 6) */
    .landing__database-stat-card:nth-child(2) {
      grid-column: 1;
      grid-row: 2;
    }

    .landing__database-stat-card:nth-child(5) {
      grid-column: 2;
      grid-row: 2;
    }

    .landing__database-stat-card:nth-child(3) {
      grid-column: 3;
      grid-row: 2;
    }

    .landing__database-stat-card:nth-child(6) {
      grid-column: 4;
      grid-row: 2;
    }

    /* Dòng 3: 40K (item 4) + 18K (item 7) – mỗi ô nửa dòng */
    .landing__database-stat-card:nth-child(4) {
      grid-column: 1 / span 2;
      grid-row: 3;
    }

    .landing__database-stat-card:nth-child(7) {
      grid-column: 3 / span 2;
      grid-row: 3;
    }
  }
}

/* ===== PHONE ≤ 640px — 3 dòng cố định ===== */
@media (max-width: 640px) {
  .landing__database-stats {
    /* reset mọi layout cũ */
    display: grid !important;
    grid-template-areas: none !important;
    grid-template-columns: repeat(4, 1fr) !important;
    /* 4 cột đều nhau */
    grid-auto-rows: auto;
    gap: 10px !important;

    /* tránh tràn nội dung */
    .landing__database-stat-card {
      min-width: 0;
      min-height: 106px;
      padding: 14px;
      border-radius: 10px;
    }

    .landing__database-stat-number {
      font-size: clamp(20px, 6vw, 30px) !important;
      line-height: 1.1;
      white-space: nowrap;
    }

    .landing__database-stat-label {
      font-size: clamp(11px, 3.6vw, 12px) !important;
      line-height: 1.25;
    }

    /* ------- Dòng 1: 230K+ (item 1) full-width ------- */
    .landing__database-stat-card:nth-child(1) {
      grid-column: 1 / -1 !important;
      /* span 4 cột */
      grid-row: 1 !important;
    }

    /* ------- Dòng 2: 77K (2), 29K (5), 14K (3), 42K (6) ------- */
    .landing__database-stat-card:nth-child(2) {
      grid-column: 1 !important;
      grid-row: 2 !important;
    }

    .landing__database-stat-card:nth-child(5) {
      grid-column: 2 !important;
      grid-row: 2 !important;
    }

    .landing__database-stat-card:nth-child(3) {
      grid-column: 3 !important;
      grid-row: 2 !important;
    }

    .landing__database-stat-card:nth-child(6) {
      grid-column: 4 !important;
      grid-row: 2 !important;
    }

    /* ------- Dòng 3: 40K (4) + 18K (7) — mỗi ô nửa dòng ------- */
    .landing__database-stat-card:nth-child(4) {
      grid-column: 1 / span 2 !important;
      grid-row: 3 !important;
    }

    .landing__database-stat-card:nth-child(7) {
      grid-column: 3 / span 2 !important;
      grid-row: 3 !important;
    }
  }
}

/* ===== PHABLET 641–820px: 2 cột, 230K+ lên đầu ===== */
@media (min-width: 641px) and (max-width: 820px) {
  .landing__database-stats {
    display: grid !important;
    grid-template-areas: none !important;
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 12px !important;

    .landing__database-stat-card {
      min-width: 0;
      min-height: 120px;
      padding: 16px;
    }

    .landing__database-stat-number {
      font-size: clamp(22px, 3.8vw, 34px) !important;
      white-space: nowrap;
      line-height: 1.1;
    }

    .landing__database-stat-label {
      font-size: clamp(11px, 2vw, 13px) !important;
    }

    /* Row 1: 230K+ full-width */
    .landing__database-stat-card:nth-child(1) {
      grid-column: 1 / -1 !important;
      grid-row: 1 !important;
    }

    /* Row 2: 77K+ full-width */
    .landing__database-stat-card:nth-child(2) {
      grid-column: 1 / -1 !important;
      grid-row: 2 !important;
    }

    /* Row 3: 29K | 42K */
    .landing__database-stat-card:nth-child(5) {
      grid-column: 1 !important;
      grid-row: 3 !important;
    }

    .landing__database-stat-card:nth-child(6) {
      grid-column: 2 !important;
      grid-row: 3 !important;
    }

    /* Row 4: 14K | 18K */
    .landing__database-stat-card:nth-child(3) {
      grid-column: 1 !important;
      grid-row: 4 !important;
    }

    .landing__database-stat-card:nth-child(7) {
      grid-column: 2 !important;
      grid-row: 4 !important;
    }

    /* Row 5: 40K+ full-width */
    .landing__database-stat-card:nth-child(4) {
      grid-column: 1 / -1 !important;
      grid-row: 5 !important;
    }
  }
}

/* ===== Laptop 992–1199px: 8 cột – 2 hàng gọn ===== */
@media (min-width: 992px) and (max-width: 1199.98px) {
  .landing__database-stats {
    display: grid !important;
    grid-template-columns: repeat(8, 1fr) !important;
    gap: 12px !important;
    --card-min-h: 120px;

    .landing__database-stat-card {
      min-width: 0;
    }

    /* Hàng 1 */
    /* 1) 230K+ – chỉ 1 hàng, 4 cột trái */
    .landing__database-stat-card:nth-child(1) {
      grid-column: 1 / span 4 !important;
      grid-row: 1 !important;
    }

    /* 2) 77K+ – cột 5..7 */
    .landing__database-stat-card:nth-child(2) {
      grid-column: 5 / span 3 !important;
      grid-row: 1 !important;
    }

    /* 3) 14K+ – cột 8 */
    .landing__database-stat-card:nth-child(3) {
      grid-column: 8 / span 1 !important;
      grid-row: 1 !important;
    }

    /* Hàng 2 – xếp đủ 4 ô còn lại */
    /* 5) 29K+ – cột 1..2 */
    .landing__database-stat-card:nth-child(5) {
      grid-column: 1 / span 2 !important;
      grid-row: 2 !important;
    }

    /* 6) 42K+ – cột 3..4 */
    .landing__database-stat-card:nth-child(6) {
      grid-column: 3 / span 2 !important;
      grid-row: 2 !important;
    }

    /* 4) 40K+ – cột 5..6 */
    .landing__database-stat-card:nth-child(4) {
      grid-column: 5 / span 2 !important;
      grid-row: 2 !important;
    }

    /* 7) 18K+ – cột 7..8 */
    .landing__database-stat-card:nth-child(7) {
      grid-column: 7 / span 2 !important;
      grid-row: 2 !important;
    }
  }
}

@media (max-width: 380px) {
  .landing__database-stats {
    /* 2 cột rộng rãi, thẻ cao vừa mắt */
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 10px !important;
    --card-min-h: 112px;

    .landing__database-stat-card {
      min-height: var(--card-min-h) !important;
      padding: 14px !important;
    }

    .landing__database-stat-number {
      /* số không bị bể hàng */
      font-size: clamp(22px, 8vw, 28px) !important;
      line-height: 1.1 !important;
      white-space: nowrap !important;
    }

    .landing__database-stat-label {
      font-size: clamp(11px, 3.6vw, 12px) !important;
      line-height: 1.25 !important;
    }

    /* Hàng 1: 230K+ full width */
    .landing__database-stat-card:nth-child(1) {
      grid-column: 1 / -1 !important;
      grid-row: 1 !important;
    }

    /* Hàng 2: 77K | 29K */
    .landing__database-stat-card:nth-child(2) {
      grid-column: 1 !important;
      grid-row: 2 !important;
    }

    .landing__database-stat-card:nth-child(5) {
      grid-column: 2 !important;
      grid-row: 2 !important;
    }

    /* Hàng 3: 14K | 42K */
    .landing__database-stat-card:nth-child(3) {
      grid-column: 1 !important;
      grid-row: 3 !important;
    }

    .landing__database-stat-card:nth-child(6) {
      grid-column: 2 !important;
      grid-row: 3 !important;
    }

    /* Hàng 4: 40K | 18K */
    .landing__database-stat-card:nth-child(4) {
      grid-column: 1 !important;
      grid-row: 4 !important;
    }

    .landing__database-stat-card:nth-child(7) {
      grid-column: 2 !important;
      grid-row: 4 !important;
    }
  }
}

/* ===== iPhone 12/13 mini – 390px (đến ~420px) ===== */
@media (min-width: 381px) and (max-width: 420px) {
  .landing__database-stats {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 12px !important;
    --card-min-h: 120px;

    .landing__database-stat-card {
      min-height: var(--card-min-h) !important;
      padding: 16px !important;
    }

    .landing__database-stat-number {
      font-size: clamp(24px, 7vw, 32px) !important;
      line-height: 1.1 !important;
      white-space: nowrap !important;
    }

    .landing__database-stat-label {
      font-size: clamp(11px, 2.8vw, 13px) !important;
    }

    /* Hàng 1: 230K+ full width */
    .landing__database-stat-card:nth-child(1) {
      grid-column: 1 / -1 !important;
      grid-row: 1 !important;
    }

    /* Hàng 2: 77K | 29K */
    .landing__database-stat-card:nth-child(2) {
      grid-column: 1 !important;
      grid-row: 2 !important;
    }

    .landing__database-stat-card:nth-child(5) {
      grid-column: 2 !important;
      grid-row: 2 !important;
    }

    /* Hàng 3: 14K | 42K */
    .landing__database-stat-card:nth-child(3) {
      grid-column: 1 !important;
      grid-row: 3 !important;
    }

    .landing__database-stat-card:nth-child(6) {
      grid-column: 2 !important;
      grid-row: 3 !important;
    }

    /* Hàng 4: 40K | 18K */
    .landing__database-stat-card:nth-child(4) {
      grid-column: 1 !important;
      grid-row: 4 !important;
    }

    .landing__database-stat-card:nth-child(7) {
      grid-column: 2 !important;
      grid-row: 4 !important;
    }
  }
}

@media (max-width: 992px) {
  .landing__contact-wrapper {
    display: grid;
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .landing__contact-info {
    order: 1;
    max-width: 760px;
  }

  .landing__contact-form {
    order: 2;
  }

  /* form chia 2 cột */
  .landing__contact-form-el {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 14px 16px;
  }

  .landing__contact-label:nth-last-child(2) {
    grid-column: 1 / -1;
  }

  .landing__contact-submit {
    grid-column: 1 / -1;
    width: 100%;
  }

  .landing__contact-input,
  .landing__contact-textarea {
    padding: 12px 14px;
    box-sizing: border-box;
  }

  /* mô tả bên trái không bị hẹp */
  .landing__contact-description {
    width: auto;
    max-width: 720px;
  }

  /* icon gọn lại một nhịp */
  .landing__contact-icon {
    width: 36px;
    height: 36px;
  }
}

/* Bé: ≤ 576px — mọi thứ 1 cột, nút full width, khoảng chạm lớn */
@media (max-width: 576px) {
  .landing__contact-wrapper {
    gap: 18px;
  }

  .landing__contact-form-el {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .landing__contact-label {
    gap: 6px;
    font-size: 12px;
  }

  .landing__contact-input {
    height: 44px;
  }

  .landing__contact-textarea {
    min-height: 150px;
  }

  .landing__contact-submit {
    width: 100%;
    padding: 14px 18px;
    border-radius: 999px;
    font-weight: 600;
  }

  /* chữ & icon nhỏ thêm chút để khỏi wrap */
  .landing__contact-icon {
    width: 32px;
    height: 32px;
  }

  .landing__contact-text {
    font-size: 14px;
  }

  .landing__contact-title {
    font-size: 22px;
  }
}

/* ===== Back-to-Top button ===== */
.landing__to-top {
  position: fixed;
  right: min(24px, 4vw);
  bottom: min(24px, 4vh);
  z-index: 1100;

  width: 44px;
  height: 44px;
  border: 1px solid rgba(2, 18, 43, .10);
  border-radius: 999px;
  background: #0EA5E9;
  color: #fff;

  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;

  box-shadow: 0 8px 18px rgba(2, 18, 43, .18);
  transition: transform .15s ease, box-shadow .15s ease, opacity .2s ease;
}

.landing__to-top:hover {
  transform: translateY(-1px);
  box-shadow: 0 12px 26px rgba(2, 18, 43, .24);
}

.landing__to-top:active {
  transform: translateY(0);
}

.landing__to-top:focus-visible {
  outline: none;
  box-shadow: 0 0 0 4px color-mix(in srgb, #0EA5E9 22%, white);
}

@media (max-width: 576px) {
  .landing__to-top {
    width: 52px;
    height: 52px;
  }
}

/* Respect reduced-motion */
@media (prefers-reduced-motion: reduce) {
  .landing__to-top {
    transition: none;
  }
}

@media (min-width: 1280px) {
  .landing__mission-figure {
    margin-left: calc(50% - 50vw);
    margin-right: calc(50% - 50vw);
  }

  .landing__mission-image {
    display: block;
    width: 100vw;
    max-width: 1580px;
    margin: 0 auto;
  }
}

@media (min-width: 1536px) {
  .landing__mission-image {
    max-width: 1680px;
  }
}

@media (min-width: 1280px) {
  .landing__database-wrapper {
    margin-left: auto !important;
    margin-right: auto !important;
    width: var(--section-width) !important;
    max-width: var(--section-max) !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
    border-radius: 0 !important;
    overflow: visible !important;
  }

  .landing__database-wrapper .landing__database-stats {
    max-width: 100% !important;
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
  }
}

/* Dùng layout 8 cột cho 992–1279px để tránh gãy xấu */
@media (min-width: 992px) and (max-width: 1279.98px) {
  .landing__database-stats {
    display: grid !important;
    grid-template-columns: repeat(8, 1fr) !important;
    gap: 12px !important;
    --card-min-h: 120px;

    .landing__database-stat-card {
      min-width: 0;
    }

    /* Hàng 1 */
    .landing__database-stat-card:nth-child(1) {
      grid-column: 1 / span 4 !important;
      grid-row: 1 !important;
    }

    .landing__database-stat-card:nth-child(2) {
      grid-column: 5 / span 3 !important;
      grid-row: 1 !important;
    }

    .landing__database-stat-card:nth-child(3) {
      grid-column: 8 / span 1 !important;
      grid-row: 1 !important;
    }

    /* Hàng 2 */
    .landing__database-stat-card:nth-child(5) {
      grid-column: 1 / span 2 !important;
      grid-row: 2 !important;
    }

    .landing__database-stat-card:nth-child(6) {
      grid-column: 3 / span 2 !important;
      grid-row: 2 !important;
    }

    .landing__database-stat-card:nth-child(4) {
      grid-column: 5 / span 2 !important;
      grid-row: 2 !important;
    }

    .landing__database-stat-card:nth-child(7) {
      grid-column: 7 / span 2 !important;
      grid-row: 2 !important;
    }
  }
}


#database.landing__main-section {
  padding-left: 0 !important;
  padding-right: 0 !important;
  max-width: none;
  width: auto;
}

html,
body {
  overflow-x: clip;
}

.landing__main-section {
  scroll-margin-top: 112px;
}

@media (min-width: 1200px) {
  .landing__main-section {
    scroll-margin-top: 128px;
  }

  /* 1) 230K+ – 5 cột x 2 hàng (trước là 6) */
  .landing__database-stats .landing__database-stat-card:nth-child(1) {
    grid-column: 1 / span 5 !important;
    grid-row: 1 / span 2 !important;
  }

  /* 2) 77K+ – hàng 1, cột 6..9 (4 cột) */
  .landing__database-stats .landing__database-stat-card:nth-child(2) {
    grid-column: 6 / span 4 !important;
    /* 6-9 */
    grid-row: 1 !important;
  }

  /* 3) 14K+ – hàng 1, cột 10 */
  .landing__database-stats .landing__database-stat-card:nth-child(3) {
    grid-column: 10 / span 1 !important;
    grid-row: 1 !important;
  }

  .landing__database-stats .landing__database-stat-card:nth-child(4) {
    grid-column: 11 / span 2 !important;
    /* 11-12, ngang bằng 18K+ */
    grid-row: 1 !important;
  }

  /* 5) 42K+ – hàng 2, cột 6..7 (2 cột) */
  .landing__database-stats .landing__database-stat-card:nth-child(5) {
    grid-column: 6 / span 2 !important;
    /* 6-7 */
    grid-row: 2 !important;
  }

  /* 6) 29K+ – hàng 2, cột 8..10 (3 cột) */
  .landing__database-stats .landing__database-stat-card:nth-child(6) {
    grid-column: 8 / span 3 !important;
    /* 8-10 */
    grid-row: 2 !important;
  }

  /* 7) 18K+ – hàng 2, cột 11..12 (2 cột)  */
  .landing__database-stats .landing__database-stat-card:nth-child(7) {
    grid-column: 11 / span 2 !important;
    /* 11-12 */
    grid-row: 2 !important;
  }

}

#articles.landing__main-section {
  padding-top: 2px;
}

#articles.landing__main-section {
  scroll-margin-top: 140px;
}

section#chatbot.landing__main-section {
  padding: 0 !important;
}

.landing__chatbot {
  padding: 0 !important;
}

.landing {
  position: relative;
  min-height: 100vh;

  font-family: "Segoe UI", system-ui, -apple-system, BlinkMacSystemFont,
    "Segoe UI Variable", "SF Pro Text", "Helvetica Neue", Arial, sans-serif;

  background-image: linear-gradient(135deg,
      #e4f3ff 0%,
      #e7f0ff 45%,
      #fff4c2 80%,
      #ffe18a 100%);

  background-size: 160% 160%;

  background-attachment: fixed;

  animation: landingGradientShift 10s ease-in-out infinite alternate;
}

/* Animation cho gradient */
@keyframes landingGradientShift {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

$mission-bg-color: #002b55;
$mission-text-color: #ffffff;

// ===== MISSION SECTION =====
.landing__mission {
  position: relative;
  background: linear-gradient(135deg, #002b55 0%, #004085 100%);
  color: $mission-text-color;
  padding: 64px 40px;
  min-height: 420px;
  border-radius: 24px;
  box-shadow: 0 18px 40px rgba(15, 23, 42, 0.32);
  margin-bottom: clamp(40px, 7vh, 72px);

  overflow: hidden;
}

.landing__mission-bg-overlay {
  position: absolute;
  inset: 0;
  background-image: url("/assets/images/pages/landing/bg-wave.png");
  background-repeat: no-repeat;
  background-position: center right;
  background-size: cover;
  opacity: 0.22;
  pointer-events: none;
  z-index: 1;
}

.landing__mission-container {
  position: relative;
  z-index: 2;
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 40px;
}

.landing__mission-visual {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 360px;
}

.landing__mission-content {
  flex: 1;
  padding-left: 56px;

  @media (max-width: 992px) {
    padding-left: 0;
    text-align: center;
  }

  .mission-title {
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 20px;
    color: #ffffff;
  }

  .mission-desc {
    font-size: 16px;
    line-height: 1.7;
    margin-bottom: 24px;
    color: #e4edf7;
    text-align: justify;
    padding: 0 8px;

    @media (max-width: 992px) {
      text-align: center;
    }
  }

  .mission-signature {
    margin-top: 24px;
    font-style: italic;
    font-size: 15px;
    text-align: right;
    color: #e9f3ff;
    padding: 0 8px;

    strong {
      display: block;
      margin-top: 4px;
      color: #ffffff;
    }
  }
}

/* Mobile: xếp dọc visual & text */
@media (max-width: 992px) {
  .landing__mission-container {
    flex-direction: column;
    text-align: center;
  }

  .landing__mission-visual {
    order: 1;
  }

  .landing__mission-content {
    order: 2;
  }
}

// --- ANIMATION STYLES ---
.network-anim-wrapper {
  position: relative;
  width: 350px;
  height: 350px;
  display: flex;
  justify-content: center;
  align-items: center;

  @media (min-width: 993px) {
    transform: translateX(-50px);
  }

  @media (max-width: 576px) {
    transform: scale(0.8);
  }
}

.node-center {
  width: 110px;
  height: 110px;
  background: #fff;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
  box-shadow: 0 0 30px rgba(0, 153, 255, 0.5);
  animation: pulse-glow 3s infinite;

  .text-cls {
    color: #008FE3;
    font-size: 28px;
    font-weight: 900;
  }
}

.dashed-orbit {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 1px dashed rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  animation: spin 60s linear infinite;
}

.satellite-wrapper {
  position: absolute;
  width: 100%;
  height: 100%;
  animation: spin 20s linear infinite; // Tốc độ xoay của các vệ tinh
}

.satellite-item {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 60px;
  height: 60px;
  margin-left: -30px;
  margin-top: -30px;

  .icon-box {
    width: 100%;
    height: 100%;
    background: #fff;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);

    // Xoay ngược để icon luôn đứng thẳng
    animation: spin-reverse 20s linear infinite;

    img {
      width: 30px;
      height: 30px;
      object-fit: contain;
      transform: translateX(-4px);
    }

    .material-fallback {
      font-size: 24px;
    }
  }
}

.item-1 {
  transform: rotate(0deg) translate(175px) rotate(0deg);
}

.item-2 {
  transform: rotate(120deg) translate(175px) rotate(-120deg);
}

.item-3 {
  transform: rotate(240deg) translate(175px) rotate(-240deg);
}

// --- KEYFRAMES ---
@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@keyframes spin-reverse {
  from {
    transform: rotate(360deg);
  }

  to {
    transform: rotate(0deg);
  }
}

@keyframes pulse-glow {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
  }

  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 20px rgba(255, 255, 255, 0);
  }

  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
  }
}

.icon-box .material-fallback {
  display: none;
}

.landing__intro-metrics {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;

  width: 100%;
  max-width: 1400px;
  margin-top: 10px !important;
  padding-top: 0 !important;
  /* Reset các style cũ nếu có */
  gap: 0;
  border-bottom: none;

  .metric {
    flex: 1;
    position: relative;
    padding: 0 24px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;

    &:not(:last-child)::after {
      content: '';
      position: absolute;
      right: 0;
      top: 10%;
      /* Cách đỉnh 10% */
      height: 80%;
      /* Chiều cao đường gạch là 80% */
      width: 1px;
      background-color: #e0e0e0;
      /* Màu xám nhạt */
    }

    .metric-inner {
      text-align: left;
      /* Căn trái toàn bộ nội dung */
      width: 100%;
    }

    .metric-value {
      /* Style cho số to */
      font-size: 32px;
      font-weight: 700;
      color: #111827;
      /* Màu đen đậm */
      margin-bottom: 8px;
      line-height: 1.2;
      white-space: nowrap;
      /* Bắt buộc số và dấu + nằm trên 1 dòng */
    }

    .metric-label {
      font-size: 15px;
      color: #6b7280;
      line-height: 1.5;
      font-weight: 400;
    }
  }

  @media (max-width: 992px) {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px 20px;
    padding: 20px 16px;

    .metric {
      padding: 0;
      border: none !important;

      &:not(:last-child)::after {
        display: none;
      }
    }
  }

  /* --- Responsive Mobile nhỏ (Dưới 576px) --- */
  // @media (max-width: 576px) {
  //   grid-template-columns: 1fr;

  //   .metric-value {
  //     font-size: 28px;
  //   }
  // }
}