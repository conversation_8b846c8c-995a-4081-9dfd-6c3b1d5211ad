import { Component, Input, Output, EventEmitter } from '@angular/core';
import { Router } from '@angular/router';
import { LoadingService } from 'app/shared/loading.service';
export interface Article {
  id: string;
  title: string;
  description?: string;
  date: string;
  imageSrc: string;
  imageAlt: string;
  link: string;
  slug?: string;
  status?: 'DRAFT' | 'PUBLISHED' | string;
  type?: 'NORMAL' | 'HOT' | string;
  coverImage?: string;
  publishedAt?: string;
  content?: string;
}

@Component({
  selector: 'app-landing-article',
  templateUrl: './landing-article.component.html',
  styleUrls: ['./landing-article.component.scss']
})
export class LandingArticleComponent {
  @Input() article: Article;
  @Input() refreshCallback?: () => void;
  @Output() imageExpired = new EventEmitter<string>();
  @Output() imageRefreshed = new EventEmitter<string>();

  constructor(private router: Router, private loading: LoadingService) { }

  openArticle(ev: MouseEvent) {
    ev.preventDefault();
    this.loading.articleGate$.next(true);
    this.loading.navigating$.next(true);
    // Điều hướng
    const link = ['/pages', 'articles', this.article.slug || this.article.id];
    this.router.navigate(link);
  }

  onImageExpired(url: string): void {
    console.log(`Image expired for article ${this.article.id}:`, url);
    this.imageExpired.emit(url);
  }

  onImageRefreshed(url: string): void {
    console.log(`Image refreshed for article ${this.article.id}:`, url);
    this.imageRefreshed.emit(url);
  }
}
