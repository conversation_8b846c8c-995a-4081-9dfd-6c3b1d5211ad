import { TestBed } from '@angular/core/testing';
import { ImageRefreshService } from './image-refresh.service';

describe('ImageRefreshService', () => {
  let service: ImageRefreshService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(ImageRefreshService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should register and unregister images', () => {
    const testUrl = 'https://test-bucket.s3.amazonaws.com/test-image.jpg';
    
    // Register image
    service.registerImage(testUrl);
    const status = service.getImageStatus(testUrl);
    expect(status).toBeTruthy();
    expect(status?.url).toBe(testUrl);
    expect(status?.isValid).toBe(true);

    // Unregister image
    service.unregisterImage(testUrl);
    const statusAfterUnregister = service.getImageStatus(testUrl);
    expect(statusAfterUnregister).toBeNull();
  });

  it('should configure service settings', () => {
    const config = {
      checkInterval: 10000,
      retryAttempts: 5,
      retryDelay: 2000
    };

    service.configure(config);
    // Since config is private, we test indirectly by checking behavior
    expect(service).toBeTruthy();
  });

  it('should clear all registered images', () => {
    const testUrl1 = 'https://test-bucket.s3.amazonaws.com/test-image1.jpg';
    const testUrl2 = 'https://test-bucket.s3.amazonaws.com/test-image2.jpg';
    
    service.registerImage(testUrl1);
    service.registerImage(testUrl2);
    
    expect(service.getImageStatus(testUrl1)).toBeTruthy();
    expect(service.getImageStatus(testUrl2)).toBeTruthy();
    
    service.clearAll();
    
    expect(service.getImageStatus(testUrl1)).toBeNull();
    expect(service.getImageStatus(testUrl2)).toBeNull();
  });

  it('should not register duplicate images', () => {
    const testUrl = 'https://test-bucket.s3.amazonaws.com/test-image.jpg';
    
    service.registerImage(testUrl);
    service.registerImage(testUrl); // Register same URL again
    
    const status = service.getImageStatus(testUrl);
    expect(status).toBeTruthy();
    expect(status?.url).toBe(testUrl);
  });

  it('should handle empty or invalid URLs', () => {
    service.registerImage('');
    service.registerImage(null as any);
    service.registerImage(undefined as any);
    
    expect(service.getImageStatus('')).toBeNull();
    expect(service.getImageStatus(null as any)).toBeNull();
    expect(service.getImageStatus(undefined as any)).toBeNull();
  });
});
