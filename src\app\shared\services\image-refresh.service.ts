import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, timer, of } from 'rxjs';
import { catchError, switchMap, tap } from 'rxjs/operators';

export interface ImageRefreshConfig {
  checkInterval?: number; // milliseconds, default 5 minutes
  retryAttempts?: number; // default 3
  retryDelay?: number; // milliseconds, default 1000
}

export interface ImageStatus {
  url: string;
  isValid: boolean;
  lastChecked: number;
  retryCount: number;
}

@Injectable({
  providedIn: 'root'
})
export class ImageRefreshService {
  private imageStatusMap = new Map<string, ImageStatus>();
  private refreshSubject = new BehaviorSubject<string[]>([]);
  private config: Required<ImageRefreshConfig> = {
    checkInterval: 5 * 60 * 1000, // 5 minutes
    retryAttempts: 3,
    retryDelay: 1000
  };

  constructor() {
    // Start periodic check
    this.startPeriodicCheck();
  }

  /**
   * Configure the service
   */
  configure(config: ImageRefreshConfig): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * Register an image URL for monitoring
   */
  registerImage(url: string): void {
    if (!url || this.imageStatusMap.has(url)) {
      return;
    }

    this.imageStatusMap.set(url, {
      url,
      isValid: true,
      lastChecked: Date.now(),
      retryCount: 0
    });
  }

  /**
   * Unregister an image URL from monitoring
   */
  unregisterImage(url: string): void {
    this.imageStatusMap.delete(url);
  }

  /**
   * Check if a specific image is still valid
   */
  checkImageValidity(url: string): Observable<boolean> {
    return new Observable<boolean>(observer => {
      const img = new Image();
      
      const timeout = setTimeout(() => {
        observer.next(false);
        observer.complete();
      }, 10000); // 10 second timeout

      img.onload = () => {
        clearTimeout(timeout);
        observer.next(true);
        observer.complete();
      };

      img.onerror = () => {
        clearTimeout(timeout);
        observer.next(false);
        observer.complete();
      };

      img.src = url;
    });
  }

  /**
   * Get observable for expired image URLs
   */
  getExpiredImages(): Observable<string[]> {
    return this.refreshSubject.asObservable();
  }

  /**
   * Manually trigger check for specific image
   */
  checkImage(url: string): Observable<boolean> {
    return this.checkImageValidity(url).pipe(
      tap(isValid => {
        const status = this.imageStatusMap.get(url);
        if (status) {
          status.isValid = isValid;
          status.lastChecked = Date.now();
          
          if (!isValid) {
            status.retryCount++;
            this.notifyExpiredImage(url);
          } else {
            status.retryCount = 0;
          }
        }
      })
    );
  }

  /**
   * Get current status of an image
   */
  getImageStatus(url: string): ImageStatus | null {
    return this.imageStatusMap.get(url) || null;
  }

  /**
   * Clear all registered images
   */
  clearAll(): void {
    this.imageStatusMap.clear();
  }

  private startPeriodicCheck(): void {
    timer(0, this.config.checkInterval).subscribe(() => {
      this.checkAllImages();
    });
  }

  private checkAllImages(): void {
    const now = Date.now();
    const expiredUrls: string[] = [];

    this.imageStatusMap.forEach((status, url) => {
      // Skip if recently checked or already marked as invalid
      if (now - status.lastChecked < this.config.checkInterval || 
          (!status.isValid && status.retryCount >= this.config.retryAttempts)) {
        return;
      }

      this.checkImageValidity(url).pipe(
        catchError(() => of(false))
      ).subscribe(isValid => {
        status.isValid = isValid;
        status.lastChecked = now;

        if (!isValid) {
          status.retryCount++;
          expiredUrls.push(url);
        } else {
          status.retryCount = 0;
        }
      });
    });

    if (expiredUrls.length > 0) {
      this.refreshSubject.next(expiredUrls);
    }
  }

  private notifyExpiredImage(url: string): void {
    const currentExpired = this.refreshSubject.value;
    if (!currentExpired.includes(url)) {
      this.refreshSubject.next([...currentExpired, url]);
    }
  }
}
