/* ===== Variables riêng cho header ===== */
:host {
  --primary-landing-color: #008fe3;
  --secondary-landing-color: #9e9e9e;
  --header-h: 80px;
}

/* ===== Header chính ===== */
.landing__header {
  display: flex;
  align-items: center;
  justify-content: space-between;

  height: var(--header-h);
  // padding: 16px 32px;
  padding: 16px 8%;
  position: sticky;
  top: 0;
  z-index: 1000;
  left: 0;
  right: 0;
  background-color: transparent;
  transition: background-color 0.2s ease, box-shadow 0.2s ease;

  &.is-scrolled {
    position: fixed;
    left: 0;
    right: 0;
    background-color: #ffffff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }

  /* Logo */
  &-logo {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 0 0 auto;
    margin-right: 24px;

    &-image {
      display: flex;
      align-items: center;
    }

    &-image img {
      display: block;
      height: 36px;
      margin-top: 11px;
      width: auto;
    }

    &-separator {
      height: 28px;
      border-left: 1px solid #757575;
    }

    &-text {
      font-size: 32px;
      font-weight: 700;
      color: var(--primary-landing-color);
    }
  }

  &-nav {
    flex: 1 1 auto;
    display: flex;
    justify-content: center;
  }

  &-nav .landing__header-nav-list {
    display: flex;
    align-items: center;
    gap: 0;
    margin: 0;
    padding: 0;
    list-style: none;
    transform: translateX(-30px);
  }

  &-nav .landing__header-nav-item {
    display: inline-flex;
    align-items: center;
    box-sizing: border-box;

    font-size: 16px;
    line-height: 1;
    padding-inline: 20px;
    padding-block: 0;
    white-space: nowrap;
    margin: 0;

    cursor: pointer;
    font-weight: 600;
    color: var(--secondary-landing-color);
    position: relative;
    transition: color 0.2s ease;

    &:hover {
      color: var(--primary-landing-color);
    }

    &.active {
      color: var(--primary-landing-color);

      &::after {
        content: "";
        position: absolute;
        left: 0;
        right: 0;
        bottom: -10px;
        height: 2px;
        border-radius: 2px;
        background-color: var(--primary-landing-color);
      }
    }

    a {
      all: unset; // reset style mặc định của a
      display: inline-flex;
      align-items: center;
      height: 100%;
      cursor: inherit;
      color: inherit;
    }

  }

  /* Nút đăng nhập */
  &-action {
    flex: 0 0 auto;
    /* cố định ở bên phải */
    text-decoration: none;

    &>.landing__header-login-btn {
      background-color: color-mix(in srgb, var(--primary-landing-color) 10%, white);
      color: var(--primary-landing-color);
      font-weight: 600;
      cursor: pointer;

      padding: 16px 32px;
      border-radius: 41px;
      border: none;
      transition: all 0.3s ease;

      &:hover {
        background-color: var(--primary-landing-color);
        color: #fff;
      }
    }
  }
}

/* ===== Burger button (mobile) ===== */
.landing__burger {
  display: none;
  /* ẩn ở desktop */
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
  width: 40px;
  height: 40px;
  padding: 8px;
  margin-right: 10px;
  border: 1px solid #e6ecf5;
  background: rgba(14, 165, 233, 0.06);
  border-radius: 10px;
  cursor: pointer;
}

.landing__burger span {
  width: 22px;
  height: 3px;
  border-radius: 2px;
  background: var(--primary-landing-color);
  display: block;
  transition: transform .2s ease, opacity .2s ease;
}

.landing__burger:hover {
  filter: brightness(1.03);
}

/* ===== Drawer (mobile) & backdrop ===== */
.landing__drawer-backdrop {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.35);
  z-index: 999;
}

.landing__drawer {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  width: min(84vw, 320px);
  height: 100vh;
  background: #fff;
  box-shadow: 2px 0 16px rgba(0, 0, 0, 0.12);
  transform: translateX(-100%);
  transition: transform .22s ease-out;
  display: flex;
  flex-direction: column;

  &.open {
    transform: translateX(0);
  }

  &-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 18px;
    border-bottom: 1px solid #eee;
  }

  &-title {
    font-weight: 700;
    color: #0b1526;
  }

  &-close {
    border: 0;
    background: transparent;
    font-size: 20px;
    line-height: 1;
    cursor: pointer;
  }

  &-nav {
    padding: 12px 8px;
  }

  &-list {
    list-style: none;
    margin: 0;
    padding: 0;
  }

  &-item {
    padding: 12px 14px;
    border-radius: 10px;
    cursor: pointer;
    font-weight: 600;
    color: #1f2937;

    &:hover {
      background: #f3f5f8;
      color: var(--primary-landing-color);
    }
  }

  &-login {
    margin-top: 6px;
    color: #fff;
    background: var(--primary-landing-color);
    text-align: center;
  }
}

/* ===== Responsive for header ===== */
@media (max-width: 992px) {
  .landing__header {
    padding: 12px 16px;
  }

  .landing__burger {
    display: inline-flex;
  }

  .landing__header-nav,
  .landing__header-action {
    display: none !important;
  }
}

/* Accessibility */
.landing__drawer:focus-visible,
.landing__burger:focus-visible,
.landing__header-login-btn:focus-visible {
  outline: none;
  box-shadow: 0 0 0 3px color-mix(in srgb, var(--primary-landing-color) 30%, white);
}

:host ::ng-deep .landing__main-section {
  scroll-margin-top: calc(var(--header-h) + 8px);
}

.landing__header-logo {
  display: flex;
  align-items: center;
  cursor: pointer;
  pointer-events: auto !important;
}