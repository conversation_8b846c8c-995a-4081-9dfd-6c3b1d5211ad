import { Component, OnInit, ViewChild, TemplateRef, AfterViewInit, AfterViewChecked } from "@angular/core";
import { Router } from "@angular/router";
import { NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { environment } from "environments/environment";
import { ViewDetailFileService } from "app/layout/components/view-detail-file/view-detail-file.service";
import { ListDocumentService } from "app/main/apps/quan-ly-van-ban/detail-work-space/list-document/list-document.service";
import { ShowContent } from "app/models/ShowContent";
import { FormType } from "app/models/FormType";
import * as feather from 'feather-icons';
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { InterceptorSkipHeader } from "@core/components/loading/loading.interceptor";
import { ToastrService } from 'ngx-toastr';

interface DocType {
  label: string;
  value: string;
  count: number;
  checked: boolean;
}
interface StatusOption {
  label: string;
  value: string;
  checked: boolean;
}
interface FileItem {
  id?: number | string;
  index: string;      // Số thứ tự (01., 02.)
  fullTitle: string;  // Tiêu đề đầy đủ
  snippet: string;    // Trích yếu
  status: string;     // Trạng thái hiệu lực
  dateIssued: string; // Ngày ban hành
  dateApplied: string;// Ngày áp dụng
  
  name: string;       // Dùng trong viewFile()
  tag: string;        // Dùng trong getter files() để lọc loại văn bản
  date: string;       // Dùng hiển thị ngày chung
  size?: string;      // Có thể để optional nếu không dùng nữa
  raw?: any;
  isExpanded?: boolean;
}

interface EsLoaiVanBan {
  key: string;
  doc_count: number;
}

interface SearchByQueryResponse {
  status: number;
  msg: string;
  data: any[];
  total: number;
  statistic: { label: string; count: number }[];
  is_fallback: boolean;
  is_paging: boolean;
}

@Component({
  selector: "app-commercial-search",
  templateUrl: "./commercial-search.component.html",
  styleUrls: ["./commercial-search.component.scss"],
})
export class CommercialSearchComponent implements OnInit {
  // ====== MODAL XEM TOÀN VĂN ======
  @ViewChild("viewFileModal") viewFileModal!: TemplateRef<any>;
  public FormType = FormType;
  public types: FormType;

  // sidebar “Loại văn bản”
  docTypes: DocType[] = [];

  activeTab: "org" | "personal" = "org";
  searchKeyword = "";

  orgFiles: FileItem[] = [];
  personalFiles: FileItem[] = [];

  pageSize = 10;
  currentPage = 1;

  isLoading = false;
  hasSearched = false;
  totalResults = 0;
  searchScope: 'document' | 'article' = 'document'; // Phạm vi tìm kiếm
  resultsPerPage = 10;
  authOptions = [
    { label: 'Quốc hội', count: 1540, checked: false },
    { label: 'Chính phủ', count: 2300, checked: false },
    { label: 'Bộ Tài chính', count: 850, checked: false },
    { label: 'Bộ Tư pháp', count: 420, checked: false },
    { label: 'Bộ Kế hoạch và Đầu tư', count: 310, checked: false },
    { label: 'UBND TP Hà Nội', count: 120, checked: false }
  ];

  statusOptions: StatusOption[] = [
    { label: "Còn hiệu lực", value: "Còn hiệu lực", checked: false },
    { label: "Hết hiệu lực", value: "Hết hiệu lực", checked: false },
    { label: "Hết hiệu lực một phần", value: "Hết hiệu lực một phần", checked: false },
    { label: "Hết hiệu lực toàn bộ", value: "Hết hiệu lực toàn bộ", checked: false },
    { label: "Chưa có hiệu lực", value: "Chưa có hiệu lực", checked: false },
    { label: "Ngưng hiệu lực", value: "Ngưng hiệu lực", checked: false }
  ];

  // Biến trạng thái đóng mở các bộ lọc
  isFilterScopeOpen = true;  // Phạm vi
  isFilterAuthOpen = true;   // Cơ quan ban hành (Mới)
  isFilterTypeOpen = true;   // Loại văn bản
  isFilterStatusOpen = true; // Tình trạng
  isDocTypeExpanded = false; // Trạng thái mở rộng filter
  readonly DOC_TYPE_LIMIT = 6;

  get displayedDocTypes(): DocType[] {
    if (this.isDocTypeExpanded) {
      return this.docTypes;
    }
    return this.docTypes.slice(0, this.DOC_TYPE_LIMIT);
  }

  constructor(
    private router: Router,
    private http: HttpClient,
    private modalService: NgbModal,
    private viewDetailFileService: ViewDetailFileService,
    private listDocumentService: ListDocumentService,
    private toast: ToastrService,
  ) {}

  ngOnInit(): void {
    this.loadDocTypes();
    this.getDocTypeStats();
  }
  
  ngAfterViewInit(): void {
    feather.replace();
  }

  ngAfterViewChecked(): void {
    feather.replace();
  }

  // Hàm toggle
  toggleDocTypeExpand() {
    this.isDocTypeExpanded = !this.isDocTypeExpanded;
  }
  getDocTypeStats() {
    const url = `${environment.apiUrl}/dashboard/es-loai-van-ban-stats/`;
    this.http.post<any[]>(url, {}).subscribe({
      next: (res) => {
        this.docTypes = res.map((item, index) => ({
          label: item.key,
          value: item.key,
          count: item.doc_count,
          checked: item.key === "Tất cả"
        }));
      },
      error: (err) => console.error("Error loading doc types:", err)
    });
  }
  toggleDocType(selectedType: DocType, event: Event): void {
    const isChecked = (event.target as HTMLInputElement).checked;
    selectedType.checked = isChecked;

    if (selectedType.value === "all") {
      if (isChecked) {
        this.docTypes.forEach(t => {
          if (t.value !== "all") t.checked = false;
        });
      } else {
      }
    } else {
      const allOption = this.docTypes.find(t => t.value === "all");
      
      if (isChecked) {
        if (allOption) allOption.checked = false;
      } else {
        const hasAnyChecked = this.docTypes.some(t => t.value !== "all" && t.checked);
        if (!hasAnyChecked && allOption) {
          allOption.checked = true;
        }
      }
    }

    // Trigger Search immediately
    this.currentPage = 1;
    this.triggerSearch();
  }
  toggleStatus(option: StatusOption, event: Event): void {
    const isChecked = (event.target as HTMLInputElement).checked;
    option.checked = isChecked;
    
    // Gọi tìm kiếm lại ngay lập tức
    this.currentPage = 1;
    this.triggerSearch();
  }
  triggerSearch() {
    this.isLoading = true;
    this.hasSearched = true;
    this.orgFiles = [];

    // --- Xử lý Loại văn bản ---
    let loaiVanBanStr = "";
    // Tìm mục "Tất cả" trong mảng docTypes (lấy từ API)
    const allDocTypeOption = this.docTypes.find(t => t.value === "Tất cả");
    const isAllDocTypesChecked = allDocTypeOption ? allDocTypeOption.checked : false;

    if (!isAllDocTypesChecked) {
      loaiVanBanStr = this.docTypes
        .filter(t => t.checked && t.value !== "Tất cả")
        .map(t => t.value)
        .join(",");
    }

    // --- Xử lý Tình trạng hiệu lực (MỚI) ---
    // Lấy các mục được check và join lại bằng dấu phẩy
    const tinhTrangHieuLucStr = this.statusOptions
      .filter(s => s.checked)
      .map(s => s.value)
      .join(",");

    // Build Payload
    const body = {
      limit: this.resultsPerPage || 10,
      filter: "",
      query: this.searchKeyword || "",
      domain_list: "",
      
      loai_van_ban: loaiVanBanStr,
      
      // Gán chuỗi tình trạng vào đây
      tinh_trang_hieu_luc: tinhTrangHieuLucStr, 

      co_quan_ban_hanh: "", 
      don_vi: "",
      linh_vuc: "",
      loai_hinh_tai_lieu: "",
      must: "",
      must_not: "",
      ngay_ban_hanh_end: null,
      ngay_ban_hanh_start: null,
      ngay_co_hieu_luc_end: null,
      ngay_co_hieu_luc_start: null,
      order: "desc",
      page: this.currentPage,
      sat_nhap_bo: false,
      sat_nhap_tinh: false,
      search_legal_term: false,
      search_only_legal_title: true,
      should: "",
      so_hieu: "",
      sort: "loai_van_ban",
      statistic: true,
      ten_van_ban: "",
      threshold: 0.5,
    };

    const url = `${environment.apiUrl}/legal_search/by_query`;
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");

    this.http.post<any>(url, body, { headers }).subscribe({
      next: (res) => {
        this.isLoading = false;
        if (res) {
            this.totalResults = res.total || 0;
            this.orgFiles = this.mapResultsToFiles(res.data || []);
        }
      },
      error: (err) => {
        console.error("Search error:", err);
        this.isLoading = false;
        this.toast.error("Đã có lỗi xảy ra khi tìm kiếm");
      }
    });
  }

  private mapResultsToFiles(results: any[]): FileItem[] {
    return results.map((item, i) => {
      // Logic số thứ tự: (Trang hiện tại - 1) * số lượng + index + 1
      const globalIndex = (this.currentPage - 1) * this.pageSize + i + 1;
      const indexStr = globalIndex < 10 ? `0${globalIndex}.` : `${globalIndex}.`;

      // Ghép tiêu đề: Loại văn bản - Số hiệu - Tên/Trích yếu ngắn
      const type = item.loai_van_ban || "Văn bản";
      const number = item.so_ky_hieu || item.so_hieu || ".../...";
      const name = item.trieu_yeu || item.title || ""; 
      const fullTitle = `${type} - ${number} - ${name}`;
      let snippetContent = item.toan_van || item.trich_yeu || item.noi_dung || "Đang cập nhật nội dung...";
      return {
        id: item.id,
        // --- Mapping cho UI mới ---
        index: indexStr,
        fullTitle: fullTitle,
        snippet: snippetContent,
        status: item.tinh_trang_hieu_luc || "Đang cập nhật",
        dateIssued: this.formatDate(item.ngay_ban_hanh),
        dateApplied: this.formatDate(item.ngay_co_hieu_luc || item.ngay_ap_dung),
        
        // --- Mapping cho Logic cũ (để tránh lỗi ở các hàm khác) ---
        name: name, // Giữ name để hàm viewFile không bị lỗi
        tag: type,  // Giữ tag để bộ lọc (sidebar) hoạt động
        date: this.formatDate(item.ngay_ban_hanh),
        size: '',   // Giá trị mặc định
        raw: item,
        isExpanded: false,
      };
    });
  }
  copyTitle(text: string, event: Event): void {
    event.stopPropagation(); // Ngăn không cho mở popup xem chi tiết khi bấm copy
    
    if (!text) return;

    // Sử dụng Clipboard API hiện đại
    navigator.clipboard.writeText(text).then(() => {
      // Hiển thị thông báo (Dùng ToastrService đã inject trong constructor)
      this.toast.success('Đã sao chép tiêu đề văn bản!', 'Thành công', {
        timeOut: 2000,
        positionClass: 'toast-bottom-right'
      });
    }).catch(err => {
      console.error('Không thể sao chép: ', err);
      this.toast.error('Lỗi khi sao chép', 'Lỗi');
    });
  }
  toggleSnippet(file: FileItem, event: Event): void {
    // Quan trọng: Ngăn chặn sự kiện click lan ra thẻ cha (thẻ cha đang mở popup viewFile)
    event.stopPropagation(); 
    file.isExpanded = !file.isExpanded;
  }
  // 3. Thêm hàm lấy class màu cho trạng thái
  getStatusClass(status: string): string {
    if (!status) return '';
    const s = status.toLowerCase();
    if (s.includes('còn hiệu lực')) return 'text-success'; // Màu xanh lá
    if (s.includes('hết hiệu lực') || s.includes('ngưng hiệu lực')) return 'text-danger'; // Màu đỏ
    return 'text-warning'; // Màu vàng/cam cho các trạng thái khác
  }
  // THÊM: Đổi scope tìm kiếm
  setSearchScope(scope: 'document' | 'article') {
    this.searchScope = scope;
  }
  private loadDocTypes(): void {
    const url = `${environment.apiUrl}/dashboard/es-loai-van-ban-stats/`;

    this.http.post<EsLoaiVanBan[]>(url, {}).subscribe({
      next: (res) => {
        // API đã trả về bao gồm mục "Tất cả" ở đầu mảng
        this.docTypes = res.map((item, index) => ({
          label: item.key,
          value: item.key, // <--- QUAN TRỌNG: Thêm dòng này để sửa lỗi thiếu 'value'
          count: item.doc_count,
          checked: index === 0, // Tự động tick chọn mục đầu tiên ("Tất cả")
        }));
      },
      error: (err) => {
        console.error("loadDocTypes error", err);
        if (!this.docTypes.length) {
          this.docTypes = [{ 
            label: "Tất cả", 
            value: "Tất cả",
            count: 0, 
            checked: true 
          }];
        }
      },
    });
  }

  get totalDocs(): number {
    const allType = this.docTypes.find((t) => t.label === "Tất cả");
    return allType ? allType.count : 0;
  }

  onSubmitSearch(): void {
    const keyword = this.searchKeyword.trim();
    this.currentPage = 1;

    // chỉ coi là đã tìm khi có keyword
    this.hasSearched = !!keyword;

    if (!keyword) {
      this.orgFiles = [];
      this.personalFiles = [];
      this.totalResults = 0;
      this.isLoading = false;
      return;
    }

    this.orgFiles = [];
    this.personalFiles = [];
    this.isLoading = true;

    this.searchByQuery(keyword).subscribe({
      next: (res) => {
        this.totalResults = res.total || (res.data ? res.data.length : 0);
        this.orgFiles = this.mapResultsToFiles(res.data || []);
        this.isLoading = false;
      },
      error: (err) => {
        console.error("searchByQuery error", err);
        this.orgFiles = [];
        this.totalResults = 0;
        this.isLoading = false;
      },
    });
  }

  private searchByQuery(keyword: string) {
    const url = `${environment.apiUrl}/legal_search/by_query`;
    const body = { query: keyword };

    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");

    return this.http.post<SearchByQueryResponse>(url, body, { headers });
  }

  private formatDate(dateStr?: string): string {
    if (!dateStr) return "";
    const d = new Date(dateStr);
    if (isNaN(d.getTime())) return dateStr;
    return d.toLocaleDateString("vi-VN");
  }

  // ================== GETTER BẢNG ==================
  get files(): FileItem[] {
    const source = this.activeTab === "org" ? this.orgFiles : this.personalFiles;
    let result = [...source];

    const allType = this.docTypes[0];
    const activeDocTypes = this.docTypes
      .filter((t) => t.checked && t.label !== "Tất cả")
      .map((t) => t.label.toLowerCase());

    if (allType && !allType.checked && activeDocTypes.length > 0) {
      result = result.filter((f) =>
        activeDocTypes.includes((f.tag || "").toLowerCase())
      );
    }

    return result;
  }

  get totalPages(): number {
    return Math.max(1, Math.ceil(this.files.length / this.pageSize));
  }

  get pagedFiles(): FileItem[] {
    const start = (this.currentPage - 1) * this.pageSize;
    return this.files.slice(start, start + this.pageSize);
  }

  // ================== CLEAR / TAB / PAGE ==================
  clearSearch(): void {
    this.searchKeyword = "";
    this.currentPage = 1;
    this.orgFiles = [];
    this.personalFiles = [];
    this.totalResults = 0;
    this.hasSearched = false;
  }

  changeTab(tab: "org" | "personal"): void {
    if (this.activeTab !== tab) {
      this.activeTab = tab;
      this.currentPage = 1;
    }
  }

  gotoPage(page: number): void {
    if (page < 1 || page > this.totalPages) return;
    this.currentPage = page;
  }

  // ================== MỞ POPUP TOÀN VĂN ==================
  viewFile(file: FileItem): void {
    console.log("viewFile click:", file);

    const raw = file.raw || file;

    // Đẩy dữ liệu cho view-detail-file & list-document
    this.viewDetailFileService.fileInfor.next(raw);
    this.viewDetailFileService.clauseId2.next(null);
    this.listDocumentService.FileSearchTemp.next(raw);
    this.listDocumentService.setBehavior(ShowContent.Search);

    // Cập nhật queryParams
    this.router.navigate([], {
      queryParams: {
        fileId: file.id,
        tabs: "toanvan",
        type: "searching",
        time: new Date().getTime(),
        fileName: file.name,
        save: true,
      },
      queryParamsHandling: "merge",
    });

    // Mở modal
    this.modalOpen(this.viewFileModal, FormType.Search, "xl");
  }
  get visiblePages(): number[] {
    const total = this.totalPages;
    const current = this.currentPage;

    // Nếu tổng số trang <= 3 thì show hết
    if (total <= 3) {
      return Array.from({ length: total }, (_, i) => i + 1);
    }

    // Đang ở rất gần đầu: 1 hoặc 2 -> luôn hiện 1,2,3
    if (current <= 2) {
      return [1, 2, 3];
    }

    // Đang ở rất gần cuối: trang cuối hoặc kế cuối -> hiện total-2, total-1, total
    if (current >= total - 1) {
      return [total - 2, total - 1, total];
    }

    // Còn lại: current ở giữa
    return [current - 1, current, current + 1];
  }

  get pageNumbers(): number[] {
    const total = this.totalPages;
    const maxVisible = 10;

    if (total <= maxVisible) {
      return Array.from({ length: total }, (_, i) => i + 1);
    }

    const half = Math.floor(maxVisible / 2);
    let start = this.currentPage - half;
    let end = this.currentPage + half - 1;

    if (start < 1) {
      start = 1;
      end = maxVisible;
    } else if (end > total) {
      end = total;
      start = total - maxVisible + 1;
    }

    const pages: number[] = [];
    for (let p = start; p <= end; p++) {
      pages.push(p);
    }
    return pages;
  }

  modalOpen(
    modalTpl: TemplateRef<any>,
    type: FormType,
    size: "sm" | "lg" | "xl" | string
  ) {
    this.types = type;
    this.modalService.open(modalTpl, {
      centered: true,
      size,
      backdrop: "static",
      keyboard: true,
      scrollable: true,
    });
  }
}
