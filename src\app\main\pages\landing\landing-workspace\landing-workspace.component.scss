:host {
  display: block;
  padding: 10px 0 15px;
  //   background-color: #f8fafc;
}

.workspace-section {
  width: 94vw;
  max-width: 1400px;
  margin: 0 auto;
  font-family: 'Se<PERSON>e UI', <PERSON><PERSON>, sans-serif;
}

/* --- Header Styles --- */
.workspace-header {
  text-align: center;
  margin-bottom: 30px;

  .workspace-title {
    font-size: clamp(24px, 4vw, 32px);
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 12px;
  }

  .workspace-desc {
    font-size: 16px;
    line-height: 1.6;
    color: #64748b;
    max-width: 700px;
    margin: 0 auto;
  }
}

/* --- Diagram Layout --- */
.diagram-container {
  position: relative;
  width: 100%;
  max-width: 1100px;
  aspect-ratio: 2 / 1;
  margin: 0 auto;
}

/* SVG Background */
.svg-connections {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: none;
  opacity: 1;

  path {
    stroke: #94a3b8;
    stroke-width: 2px;
    stroke-dasharray: 6 4;

    /* stroke: #0ea5e9; */
  }
}

/* Grid Layout */
.diagram-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;

  display: grid;
  grid-template-columns: 1fr 140px 1fr;
  align-items: center;
}

.col-side {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 8% 0;
  height: 100%;
}

.col-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* --- Item Styles --- */
.diagram-item {
  display: flex;
  align-items: center;
  gap: 16px;
  transition: transform 0.3s ease;

  &:hover {
    transform: translateY(-2px);

    .icon-wrap {
      background: #e0f2fe;
      color: #0ea5e9;
      box-shadow: 0 4px 12px rgba(14, 165, 233, 0.15);
    }

    .item-text {
      color: #0ea5e9;
    }
  }

  .icon-wrap {
    width: 48px;
    height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    font-size: 20px;
    color: #64748b;
    flex-shrink: 0;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    z-index: 2;

    img,
    i,
    svg {
      width: 24px;
      height: 24px;
    }
  }

  .item-text {
    font-size: 14px;
    font-weight: 600;
    color: #334155;
    line-height: 1.4;
    white-space: nowrap;
    max-width: none;
    width: auto;
  }
}

/* Cột Trái*/
.col-left {
  align-items: flex-end;
  /* Căn toàn bộ item sang phải */
  padding-right: 100px;
  /* Khoảng hở an toàn với logo */

  .diagram-item {
    /* Đảo ngược: Text - Icon -> Để Icon nằm sát đường nối */
    flex-direction: row-reverse;
    text-align: right;
  }
}

/* Cột Phải*/
.col-right {
  align-items: flex-start;
  /* Căn toàn bộ item sang trái */
  padding-left: 100px;
  /* Khoảng hở an toàn với logo */

  .diagram-item {
    flex-direction: row;
    text-align: left;
  }
}


/* --- Logo Trung tâm --- */
.cls-circle {
  width: 120px;
  height: 120px;
  background: #ffffff;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 10px 40px -10px rgba(14, 165, 233, 0.3);
  border: 4px solid #fff;
  position: relative;
  z-index: 5;

  &::before {
    content: '';
    position: absolute;
    inset: -2px;
    border-radius: 50%;
    background: linear-gradient(135deg, #e0f2fe, #fff);
    z-index: -1;
  }

  .cls-text {
    font-size: 36px;
    font-weight: 800;
    color: #0ea5e9;
    letter-spacing: -1px;
  }
}

.item-l1,
.item-r1 {
  margin-top: 30px;
}

.item-l4,
.item-r4 {
  margin-bottom: 30px;
}

/* --- Responsive --- */
@media (max-width: 992px) {
  .workspace-section {
    width: 96vw;
    padding: 0 16px;
  }

  .workspace-header {
    margin-bottom: 40px;

    .workspace-title {
      font-size: clamp(20px, 5vw, 28px);
      margin-bottom: 16px;
    }

    .workspace-desc {
      font-size: 15px;
      line-height: 1.7;
      max-width: 600px;
    }
  }

  .diagram-container {
    height: auto;
    aspect-ratio: unset;
    padding: 24px 16px;
    max-width: 100%;
  }

  .svg-connections {
    display: none;
  }

  .diagram-grid {
    position: static;
    display: flex;
    flex-direction: column;
    gap: 32px;
    align-items: center;
  }

  .col-center {
    display: none
  }

  /* Reset alignment trên mobile - hiển thị dạng list */
  .col-left,
  .col-right {
    width: 100%;
    max-width: 500px;
    padding: 0;
    align-items: stretch;
    gap: 20px;
  }

  .col-left .diagram-item,
  .col-right .diagram-item {
    flex-direction: row;
    text-align: left;
    width: 100%;
    padding: 16px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    }
  }

  .item-text {
    max-width: none;
    white-space: normal;
    font-size: 13px;
    line-height: 1.5;
  }

  .icon-wrap {
    width: 44px;
    height: 44px;
    flex-shrink: 0;

    img,
    i,
    svg {
      width: 22px;
      height: 22px;
    }
  }

  .cls-circle {
    width: 100px;
    height: 100px;

    .cls-text {
      font-size: 30px;
    }
  }
}

/* Mobile nhỏ */
@media (max-width: 576px) {
  .workspace-section {
    width: 100%;
    padding: 0 12px;
  }

  .workspace-header {
    margin-bottom: 32px;

    .workspace-title {
      font-size: clamp(18px, 6vw, 24px);
      margin-bottom: 12px;
    }

    .workspace-desc {
      font-size: 14px;
      line-height: 1.6;
    }
  }

  .diagram-container {
    padding: 20px 12px;
  }

  .diagram-grid {
    gap: 24px;
  }

  .col-left,
  .col-right {
    max-width: 100%;
    gap: 16px;
  }

  .col-left .diagram-item,
  .col-right .diagram-item {
    padding: 14px;
    gap: 12px;
  }

  .item-text {
    font-size: 12px;
    line-height: 1.4;
  }

  .icon-wrap {
    width: 40px;
    height: 40px;

    img,
    i,
    svg {
      width: 20px;
      height: 20px;
    }
  }

  .cls-circle {
    width: 80px;
    height: 80px;

    .cls-text {
      font-size: 24px;
    }
  }
}