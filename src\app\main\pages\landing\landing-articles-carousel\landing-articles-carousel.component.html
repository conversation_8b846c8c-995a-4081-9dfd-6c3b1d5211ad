<!-- landing-articles-carousel.component.html -->
<!-- landing-articles-carousel.component.html -->
<section class="lac" *ngIf="slides && slides.length">
  <div class="lac__header">
    <!-- Custom controls -->
    <div class="lac__controls" *ngIf="slides.length > 1">
      <button
        type="button"
        class="lac__ctrl lac__ctrl--prev"
        aria-label="Previous"
        (click)="carousel.prev()"
      >
        <span class="lac__chev lac__chev--left"></span>
      </button>
      <button
        type="button"
        class="lac__ctrl lac__ctrl--next"
        aria-label="Next"
        (click)="carousel.next()"
      >
        <span class="lac__chev lac__chev--right"></span>
      </button>
    </div>
  </div>

  <ngb-carousel
    #carousel
    class="lac__carousel"
    [interval]="slides.length > 1 ? autoPlayIntervalMs : 0"
    [wrap]="slides.length > 1"
    [keyboard]="slides.length > 1"
    [pauseOnHover]="true"
    (slide)="activeId = $event.current"
  >
    <ng-template
      ngbSlide
      *ngFor="let slide of slides; let i = index"
      [id]="'s-' + i"
    >
    <div
      class="lac__grid"
      [class.lac__grid--center]="isDesktop && slide.length < itemsPerSlide"
    >
      <app-landing-article
        *ngFor="let article of slide; trackBy: trackByArticle"
        [article]="article"
        [refreshCallback]="refreshCallback"
        (imageExpired)="onImageExpired($event)"
        (imageRefreshed)="onImageRefreshed($event)"
        class="lac__item">
      </app-landing-article>
    </div>

    </ng-template>
  </ngb-carousel>

  <div class="lac__dots" *ngIf="slides.length > 1">
    <button
      type="button"
      *ngFor="let _ of slides; let i = index"
      class="lac__dot"
      [class.lac__dot--active]="activeId === 's-' + i"
      (click)="setActive(i); carousel.select('s-' + i)"
      aria-label="Go to slide {{ i + 1 }}"
    ></button>
  </div>
 
</section>

