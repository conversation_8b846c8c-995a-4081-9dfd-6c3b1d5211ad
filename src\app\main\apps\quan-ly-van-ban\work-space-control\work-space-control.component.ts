import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { Router } from "@angular/router";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { ChangeDataService } from "app/auth/service/change-data.service";
import { FormType } from "app/models/FormType";
import { ToastrService } from "ngx-toastr";
import { QuanLyVanBanService } from "../quan-ly-van-ban.service";

@Component({
  selector: "app-work-space-control",
  templateUrl: "./work-space-control.component.html",
  styleUrls: ["./work-space-control.component.scss"],
})
export class WorkSpaceControlComponent implements OnInit {
  @Input("modal") public modal: NgbActiveModal;
  @Input("title") public title: string;
  @Input("type") public type: number;
  @Input("row") public row: any;
  @Output() destroyed = new EventEmitter<void>();
  public formWorkSpace: FormGroup;
  public submitted: boolean = false;
  public isSubmitting: boolean = false; // Thêm flag để tránh submit nhiều lần
  constructor(
    private fb: FormBuilder,
    private qlvb: QuanLyVanBanService,
    private changeData: ChangeDataService,
    private _toast: ToastrService,
    private router: Router
  ) { }

  ngOnInit(): void {
    this.formWorkSpace = this.fb.group({
      name: [null, [Validators.required, Validators.maxLength(100)]],
      description: [null],
    });
    if (this.type == FormType.Update) {
      this.formWorkSpace.patchValue({ ...this.row });
    }
  }
  get f() {
    return this.formWorkSpace.controls;
  }
  submit() {
    this.submitted = true;

    // Tránh submit nhiều lần
    if (this.formWorkSpace.invalid || this.isSubmitting) {
      return;
    }

    this.isSubmitting = true; // Đánh dấu đang submit
    const formData = new FormData();
    Object.keys(this.formWorkSpace.value).forEach((key) => {
      formData.append(key, this.formWorkSpace.value[key]);
    });
    if (this.type == FormType.Create) {
      this.qlvb.addWorkSpace(formData).subscribe(
        (res) => {
          this.isSubmitting = false; // Reset flag
          if (res) {
            // this.changeData.changeData.next(true);
            localStorage.setItem("isNewUser", JSON.stringify(false)); // dánh dấu là người dùng cũ
            this._toast.success("Thêm không gian dự án", "Thành công", {
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
              closeButton: true,
            });

            // Emit destroyed event để parent component biết cần reload
            this.destroyed.emit();

            // Close modal với data để parent biết tạo thành công
            this.modal.close({ success: true, workspace: res });

            // Chỉ navigate nếu không phải được gọi từ workspace selection modal
            // Kiểm tra xem có phải đang ở trong workspace selection modal không
            const currentUrl = this.router.url;
            const disableNavigate = localStorage.getItem('disableNavigate') || 'false';
            if (!currentUrl.includes('tim-kiem-thong-minh') && disableNavigate === 'false') {
              this.router.navigate([`/quan-ly-van-ban/workspace/${res.id}`], {
                // tự động chueyern vào workspace đã tạo
                queryParams: { workSpaceName: res.name },
              });

            }
            localStorage.removeItem('disableNavigate');
          }
        },
        (error) => {
          this.isSubmitting = false; // Reset flag khi có lỗi
          this._toast.error("Thêm không gian dự án", "Thất bại", {
            positionClass: "toast-top-right",
            toastClass: "toast ngx-toastr",
            closeButton: true,
          });
        }
      );
    } else {
      this.qlvb.updateWorkSpace(formData, this.row.id).subscribe(
        (res) => {
          this.isSubmitting = false; // Reset flag
          if (res) {
            this.changeData.changeData.next(true);
            this._toast.success("Cập nhật không gian dự án", "Thành công", {
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
              closeButton: true,
            });
            this.modal.close({ success: true, workspace: res }); // Thêm success flag
          }
        },
        (error) => {
          this.isSubmitting = false; // Reset flag khi có lỗi
          this._toast.error("Cập nhật không gian dự án", "Thất bại", {
            positionClass: "toast-top-right",
            toastClass: "toast ngx-toastr",
            closeButton: true,
          });
        }
      );
    }
  }

  cancel() {
    // Dismiss modal thay vì close để không trigger reload data
    this.modal.dismiss('cancel');
  }

  onEnterKey(event: KeyboardEvent) {
    // Ngăn chặn default behavior của Enter key
    event.preventDefault();

    // Chỉ submit nếu form valid và không đang submit
    if (this.formWorkSpace.valid && !this.isSubmitting) {
      this.submit();
    }
  }

  ngOnDestroy(): void {
    // this.destroyed.emit(); // Gửi sự kiện khi bị destroy
  }
}
