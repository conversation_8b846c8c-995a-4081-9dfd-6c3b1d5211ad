import { ChangeDetectorRef, Component, HostListener, OnInit, Renderer2, TemplateRef, ViewChild, ViewEncapsulation } from "@angular/core";
import { FormControl } from "@angular/forms";
import { Router, NavigationEnd, ActivatedRoute } from "@angular/router";
import { CoreConfigService } from "@core/services/config.service";
import { NgbModal, NgbModalRef } from "@ng-bootstrap/ng-bootstrap";
import { AuthenticationService } from "app/auth/service";
import { CacheDataService } from "app/auth/service/cache-data.service";
import { ChangeDataService } from "app/auth/service/change-data.service";
import { DocumentStatus } from "app/models/DocumentStatus";
import { FormType } from "app/models/FormType";
import { WorkSpace } from "app/models/WorkSpace";
import { ToastrService } from "ngx-toastr";
import { Subject } from "rxjs";
import { debounceTime, distinctUntilChanged, takeUntil, finalize, filter, map } from "rxjs/operators";
import Swal from "sweetalert2";
import * as feather from 'feather-icons';
import { QuanLyVanBanService } from "./quan-ly-van-ban.service";
import { ShepherdService } from "angular-shepherd";
import { CmsService } from 'app/main/apps/cms/cms.service';

@Component({
  selector: "app-quan-ly-van-ban",
  templateUrl: "./quan-ly-van-ban.component.html",
  styleUrls: ["./quan-ly-van-ban.component.scss"],
  encapsulation: ViewEncapsulation.None,
})
export class QuanLyVanBanComponent implements OnInit {
  @ViewChild("editDocumentNameModal") editDocumentNameModal!: any;
  @ViewChild("modalConvertFile") modalConvertFile!: any;
  @ViewChild("modalSearchFile") modalSearchFile!: any;
  @ViewChild("modalCompareFile") modalCompareFile!: any;
  @ViewChild("modalChatbot") modalChatbot!: any;
  @ViewChild("modalWorkSpace") modalWorkSpace!: any;
  @ViewChild('editDocumentNameModal') editSearchModal!: any;

  public totalFile: number = 0;
  public pageBasic = 1;
  public currentPage = 1;
  public page_size: number = 12;
  public documentStatus = DocumentStatus;
  public _unsubscribeAll: Subject<any> = new Subject();
  public search: FormControl = new FormControl("");
  public listWorkSpace: WorkSpace[];
  public listWorkSpaceFilter: WorkSpace[];
  public type: FormType;
  public title: string;
  public row: any;
  public totalWorkSpace: number = 0;
  public limit: number[] = [10, 15, 20, 25, 30];
  public sortProjectList = ["Tên dự án", "Thời gian tạo", "Số lượng tài liệu"];
  public typeSort = true; // true: asc, false: desc
  public currentSortKey: "name" | "created_at" | "doc_count" = "name";
  public isShowDashboard: boolean = false;
  public role: string;
  public backBtnClass = "btn btn-sm btn-outline-primary";
  public nextBtnClass = "btn btn-sm btn-primary btn-next";
  public isNewUser: boolean = false;
  public isSearchActive = false;
  public isSidebarVisible = false;
  public cmsRole: string;
  private _destroy$ = new Subject<void>();
  headerOffset = 40;
  isSearching = false;
  isMobile: boolean = false;
  tempValue = '';
  tempSearchValue = '';
  private chatbotModalRef: NgbModalRef | null = null;
  constructor(
    private quanlyvanban: QuanLyVanBanService,
    private router: Router,
    private route: ActivatedRoute,
    private changeData: ChangeDataService,
    private modalService: NgbModal,
    private cacheDataService: CacheDataService,
    private toast: ToastrService,
    private authenService: AuthenticationService,
    private configApp: CoreConfigService,
    private shepherdService: ShepherdService,
    private renderer: Renderer2,
    private _cms: CmsService,
  ) {
    this.role = this.authenService.currentUserValue.role;
    this.isNewUser = JSON.parse(localStorage.getItem("isNewUser"));
  }

  private _normalize(url: string): string {
    const raw = (url || '').toString();
    return raw.split('#')[0].split('?')[0]
      .replace(/^https?:\/\/[^/]+/i, '')
      .replace(/^\/cls(?=\/)/i, '');
  }
  private _isQlVbRoot(url: string): boolean {
    const u = this._normalize(url);
    return /^\/?quan-ly-van-ban\/?$/i.test(u);
  }

  private _coreCfgSnap: any = null;

  ngOnInit(): void {

    const ua = navigator.userAgent || navigator.vendor || (window as any).opera;
    if (this.role === "USER") {
      const onQlVbRoot = this._isQlVbRoot(this.router.url);
      this.configApp.setConfig({
        layout: { menu: { hidden: !onQlVbRoot } },
      });
      this.page_size = 18;
    }
    this.cmsRole = JSON.parse(localStorage.getItem('cms_roles'))[0]?.role || "";
    console.log(this.cmsRole);
    this._cms.ensureCmsRolesLoaded().subscribe();
    this._cms.cmsRoles$
      .pipe(takeUntil(this._destroy$))
      .subscribe(roles => {
        const hasCms = Array.isArray(roles) && roles.length > 0;
        const atRoot = this._isQlVbRoot(this.router.url);
        if (hasCms && atRoot) {
          this.configApp.setConfig({ layout: { menu: { hidden: false, collapsed: false } } });
        }
      });

    this.configApp.config
      .pipe(takeUntil(this._destroy$))
      .subscribe(cfg => {
        const hidden = !!cfg?.layout?.menu?.hidden;
        this.isSidebarVisible = !hidden && this._isQlVbRoot(this.router.url);
      });
    this.configApp.config
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe(cfg => { this._coreCfgSnap = cfg; });

    // Theo dõi thay đổi route để tắt margin khi đi sâu (workspace/...) hoặc sang trang khác
    this.router.events
      .pipe(filter(e => e instanceof NavigationEnd), takeUntil(this._destroy$))
      .subscribe(() => {
        const hidden = !!this._coreCfgSnap?.layout?.menu?.hidden;
        this.isSidebarVisible = !hidden && this._isQlVbRoot(this.router.url);
      });

    this.changeData.changeData
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe((res) => {
        if (res) {
          this.getAllWorkSpace(true, false);
        }
      });
    this.search.valueChanges
      .pipe(
        takeUntil(this._unsubscribeAll),
        debounceTime(300),
        map(v => v?.trim() || ''),
        distinctUntilChanged()
      )
      .subscribe((res) => {
        this.getAllWorkSpace(true, true);
      });

    this.getAllWorkSpace(true, false);
    this.checkScreenSize();
    if (/android/i.test(ua)) {
      this.renderer.addClass(document.body, 'is-android');
    }

    // Nếu route có param showChatbot thì tự động mở chatbot
    this.route.queryParamMap
      .pipe(
        map(p => p.get('showChatbot') === 'true'),
        distinctUntilChanged()
      )
      .subscribe(show => {
        if (show) {
          setTimeout(() => {
            // this.showChatbot();
            this.modalOpen(
              this.modalChatbot,
              "Chuyển đổi văn bản",
              null,
              FormType.Chatbot,
              "xl",
              false
            );
          }, 0);
        }
      });

  }
  getAllWorkSpace(showLoading: boolean = false, resetPage: boolean = false) {
    this.isSearching = showLoading;
    if (resetPage) {
      this.currentPage = 1;
    }
    // setTimeout(() => {
    this.quanlyvanban
      .getAllWorkSpace(this.search.value, this.currentPage, this.page_size)
      .pipe(finalize(() => { this.isSearching = false; }))
      .subscribe({
        next: (res) => {
          this.listWorkSpace = res.results;
          this.listWorkSpaceFilter = res.results;
          this.totalWorkSpace = res.count;
        },
        error: () => { }
      });
    // }, 1500);
  }
  addWorkSpace() {
    this.modalOpen(
      this.modalWorkSpace,
      "Thêm dự án",
      null,
      FormType.Create,
      "sm",
      false
    );
  }
  editWorkSpace(workSpace) {
    this.modalOpen(
      this.modalWorkSpace,
      "Cập nhật dự án",
      workSpace,
      FormType.Update,
      "sm",
      false
    );
  }
  modalOpen(modalSM, title: string, row: any, type: FormType, size: string, shouldExpandOnSmallScreen: boolean = true) {
    const isSmallScreen = window.innerWidth < 992;

    if (modalSM === this.modalChatbot) {
      this.chatbotModalRef = this.modalService.open(modalSM, {
        centered: true,
        size: size,
        // Dùng modalDialogClass (Angular >= 15 + ng-bootstrap >= 15 hỗ trợ)
        modalDialogClass: (isSmallScreen && shouldExpandOnSmallScreen) ? 'modal-full-lg' : '',
      });

      this.chatbotModalRef.result.finally(() => {
        this.chatbotModalRef = null;
      });
    } else {
      const modalRef = this.modalService.open(modalSM, {
        centered: true,
        size: size,
        // Dùng modalDialogClass (Angular >= 15 + ng-bootstrap >= 15 hỗ trợ)
        modalDialogClass: (isSmallScreen && shouldExpandOnSmallScreen) ? 'modal-full-lg' : '',
      });

      // Chỉ reload data khi modal được đóng thành công (không phải dismiss/cancel)
      modalRef.result.then((result) => {
        // Modal được đóng thành công
        if (type === FormType.Search) {
          localStorage.removeItem('searchOOW');
        }
        // Chỉ reload data khi có thay đổi thực sự (có success flag)
        if (result && result.success) {
          this.getAllWorkSpace(true, false);
        }
      }).catch(() => {
        // Modal bị dismiss/cancel - không reload data
        if (type === FormType.Search) {
          localStorage.removeItem('searchOOW');
        }
        // Không reload data khi modal bị cancel/dismiss
      });
    }


    // const modalRef = this.modalService.open(modalSM, {
    //   centered: true,
    //   size: size,
    //   // Dùng modalDialogClass (Angular >= 15 + ng-bootstrap >= 15 hỗ trợ)
    //   modalDialogClass: (isSmallScreen && shouldExpandOnSmallScreen) ? 'modal-full-lg' : '',
    // });
    // modalRef.result.finally(() => {
    //   if (type === FormType.Search) {
    //     localStorage.removeItem('searchOOW');
    //   }
    //   this.getAllWorkSpace(true, false);
    // });
    this.title = title;
    this.type = type;
    this.row = row;
  }

  deleteWorkSpace(item) {
    Swal.fire({
      title: "Bạn có chắc chắn muốn xóa?",
      icon: "warning",
      reverseButtons: true,
      showCancelButton: true,
      confirmButtonColor: "#008fd3",
      cancelButtonColor: "#EEE",
      customClass: {
        confirmButton: "swal-confirm",
        cancelButton: "swal-cancel",
      },
      confirmButtonText: "Xóa",
      cancelButtonText: "Hủy",
    }).then((result) => {
      if (result.isConfirmed) {
        this.quanlyvanban.deleteWorkSpace(item.id).subscribe(
          (res) => {
            this.toast.success("Đã xoá không gian làm việc", "Thành công", {
              closeButton: true,
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
            });

            this.getAllWorkSpace(true, false);
          },
          (error) => {
            this.toast.error(error.message, "Thất bại", {
              closeButton: true,
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
            });
          }
        );
      }
    });
  }
  detailWorkSpace(workSpace: WorkSpace) {
    this.cacheDataService.clearCache();
    this.cacheDataService.clearFilter();
    this.router.navigate([`/quan-ly-van-ban/workspace/${workSpace.id}`], {
      queryParams: { workSpaceName: workSpace.name },
    });
  }

  onPageChange(e) {
    this.currentPage = e;
    this.getAllWorkSpace(true, false);
  }
  sortProject(event) {
    // Xác định trường sort dựa trên lựa chọn
    switch (event) {
      case "Tên dự án":
        this.currentSortKey = "name";
        break;
      case "Thời gian tạo":
        this.currentSortKey = "created_at";
        break;
      case "Số lượng tài liệu":
        this.currentSortKey = "doc_count";
        break;
    }
    this.sortList(this.currentSortKey, this.typeSort);
  }

  sortList(key: "name" | "created_at" | "doc_count", asc: boolean = true) {
    this.listWorkSpaceFilter = this.listWorkSpace.slice().sort((a, b) => {
      let result = 0;
      if (key === "name") {
        result = a.name.localeCompare(b.name);
      } else if (key === "created_at") {
        result =
          new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
      } else if (key === "doc_count") {
        result = a.doc_count - b.doc_count;
      }
      return asc ? result : -result;
    });
  }

  loaiSapXep() {
    this.typeSort = !this.typeSort;
    this.sortList(this.currentSortKey, this.typeSort);
  }
  showConvertFile() {
    this.modalOpen(
      this.modalConvertFile,
      "Chuyển đổi văn bản",
      null,
      FormType.Create,
      "xl"
    );
  }
  showSearchFile() {
    this.cacheDataService.clearCache();
    this.cacheDataService.clearFilter();
    this.modalOpen(
      this.modalSearchFile,
      "Tìm kiếm văn bản",
      null,
      FormType.Search,
      "xl",

    );
    localStorage.setItem('searchOOW', 'true');
  }
  showCompareFile() {
    this.modalOpen(
      this.modalCompareFile,
      "So sánh văn bản",
      null,
      FormType.Compare,
      "xl"
    );
  }
  showChatbot() {
    if (this.chatbotModalRef) {
      return;
    }

    const workspaceId = localStorage.getItem("workspace_id");
    if (!workspaceId) {
      this.authenService.logout();
      this.router.navigate(["pages/authentication/login-v2"]);
      return;
    }

    // Thêm params showChatbot=true vào URL
    this.router.navigate([], {
      queryParams: {
        showChatbot: true,
      },
      queryParamsHandling: 'merge',
    });
    // this.modalOpen(
    //   this.modalChatbot,
    //   "Chuyển đổi văn bản",
    //   null,
    //   FormType.Chatbot, user-action-dropdown.component.
    //   "xl",
    //   false
    // );
  }
  startTour() {
    this.shepherdService.start();
  }
  ngAfterViewInit() {
    feather.replace();
    this.hardKillShepherdArtifacts();

    this.shepherdService.defaultStepOptions = {
      cancelIcon: { enabled: true },
      classes: 'cls-tour',
      scrollTo: false,
      canClickTarget: false,
      useModalOverlay: true,
      highlightClass: 'cls-tour-target',
      arrow: true,

    };
    // this.shepherdService.modal = true;

    const steps = this.buildSteps();
    this.shepherdService.addSteps(steps);
  }

  private showStepWhenReady(stepId: string, selector: string, timeoutMs = 20000) {
    const tour: any = (this.shepherdService as any).tourObject;
    const start = Date.now();
    const tick = setInterval(() => {
      if (document.querySelector(selector)) {
        clearInterval(tick);
        try { tour.show(stepId); } catch { }
      }
      if (Date.now() - start > timeoutMs) clearInterval(tick);
    }, 250);
  }

  private hardKillShepherdArtifacts() {
    try { (this.shepherdService as any).tourObject?.cancel(); } catch { }
    // Xoá overlay còn sót lại
    document.querySelectorAll('.shepherd-modal-overlay-container').forEach(el => el.remove());
    document.body.classList.remove('shepherd-modal-is-visible');
    document.documentElement.classList.remove('shepherd-modal-is-visible');
    // Trả lại style body (nếu Shepherd từng set padding-right khi khoá scroll)
    document.body.style.removeProperty('padding-right');
    document.documentElement.style.removeProperty('padding-right');
    document.body.style.removeProperty('overflow');
    document.documentElement.style.removeProperty('overflow');
    // Reset scroll tuyệt đối (tránh trang bị kéo xuống)
    window.scrollTo({ top: 0, left: 0, behavior: 'auto' });
  }

  private buildSteps() {
    const total = 7; // cập nhật tổng số bước

    return [
      // ===== BƯỚC 1: Cụm tính năng =====
      {
        id: 'cluster',
        title: 'Cụm tính năng',
        text: this.makeText(
          'Bao gồm các công cụ tìm kiếm, chuyển đổi và so sánh văn bản, đáp ứng nhu cầu tra cứu và phân tích nội dung văn bản quy phạm pháp luật.',
          1, total
        ),
        attachTo: { element: '.step-1', on: 'bottom' },
        buttons: [
          // đẩy sang phải bằng ml-auto (đã có CSS hỗ trợ)
          { text: 'Tiếp tục', type: 'next', classes: 'tour-btn tour-btn--primary ml-auto' }
        ]
      },

      // ===== BƯỚC 2: Truy vấn Chatbot =====
      {
        id: 'chat',
        title: 'Tính năng Truy vấn Chatbot',
        text: this.makeText(
          'Đây là tính năng trò chuyện, đặt câu hỏi, truy vấn về các vấn đề pháp luật với câu trả lời đầy đủ, rõ ràng, chi tiết, dễ hiểu.',
          2, total
        ),
        attachTo: { element: '.chat-button', on: 'bottom' },
        buttons: [
          // nút quay lại: viền xanh + chữ xanh
          { text: 'Quay lại', type: 'back', classes: 'tour-btn tour-btn--outline-primary' },
          // nút tiếp tục: nằm bên phải
          { text: 'Tiếp tục', type: 'next', classes: 'tour-btn tour-btn--primary ml-auto' }
        ]
      },

      // ===== BƯỚC 3: Tạo không gian dự án =====
      {
        id: 'workspace',
        title: 'Tạo không gian dự án',
        text: this.makeText(
          'Tạo không gian dự án để tổ chức, lưu trữ và làm việc với tài liệu liên quan một cách khoa học, dễ quản lý.',
          3, total
        ),
        attachTo: { element: '.step-3', on: 'top' },
        buttons: [
          // có nút quay lại viền xanh + chữ xanh
          { text: 'Quay lại', type: 'back', classes: 'tour-btn tour-btn--outline-primary' },
          // “Thêm dự án” đẩy sang phải
          {
            text: 'Thêm dự án',
            classes: 'tour-btn tour-btn--primary ml-auto',
            action: () => {
              localStorage.setItem('cls_tour_resume', 'docs-list');
              try { this.shepherdService.cancel(); } catch { }
              this.hardKillShepherdArtifacts();
              this.addWorkSpace();
            }
          }
        ]
      },
      // ===== BƯỚC 4: Danh sách văn bản (panel trái) =====
      {
        id: 'docs-list',
        title: 'Danh sách văn bản',
        text: this.makeText(
          'Khu vực này cho phép quản lý toàn bộ tài liệu, văn bản bao gồm các tệp tải lên và văn bản đã được lưu từ cơ sở dữ liệu.',
          4, total
        ),
        attachTo: { element: '.ws-docs-list', on: 'right' },
        buttons: [
          { text: 'Quay lại', type: 'back', classes: 'tour-btn tour-btn--ghost' },
          { text: 'Tiếp tục', type: 'next', classes: 'tour-btn tour-btn--primary' }
        ]
      },

      // ===== BƯỚC 5: Khu vực trung tâm (panel Tìm kiếm) =====
      {
        id: 'center-area',
        title: 'Khu vực trung tâm',
        text: this.makeText(
          'Khu vực này dùng để hiển thị kết quả tìm kiếm, đối chiếu và so sánh các văn bản quy phạm pháp luật, đồng thời cung cấp nội dung chi tiết của văn bản.',
          5, total
        ),
        attachTo: { element: '.tour-view-file', on: 'right' },
        buttons: [
          { text: 'Quay lại', type: 'back', classes: 'tour-btn tour-btn--ghost' },
          { text: 'Tiếp tục', type: 'next', classes: 'tour-btn tour-btn--primary' }
        ]
      },
      {
        id: 'notes-open',
        title: 'Ghi chú và Chatbot',
        text: this.makeText(
          'Ghi chú cho phép tạo, lưu trữ và quản lý các ghi chú cá nhân liên quan khi làm việc.<br/>' +
          'Chatbot cho phép trò chuyện, đặt câu hỏi, truy vấn về các vấn đề pháp luật với câu trả lời đầy đủ rõ ràng chi tiết .',
          6, total
        ),
        attachTo: { element: '.tour-show-chatbot', on: 'left' },
        action: () => {
          // Mở panel ghi chú bằng event – DetailWorkSpace sẽ nghe và mở đúng luồng
          window.dispatchEvent(new Event('open-notes-tab'));

          // Khi vùng outlet ghi chú xuất hiện/stabilize, tự sang bước 7
          this.showStepWhenReady('notes-explain', '.detail-work-space-nav-outlet', 8000);
        },
        buttons: [
          { text: 'Quay lại', type: 'back', classes: 'tour-btn tour-btn--ghost' },
          {
            text: 'Mở rộng',
            classes: 'tour-btn tour-btn--primary',
            action: () => {
              // mở panel phải & chuyển tab Ghi chú
              window.dispatchEvent(new Event('open-notes-tab'));
              // nhảy sang bước 7 sau khi mở
              setTimeout(() => this.shepherdService.next(), 150);
            }
          }
        ]
      },

      // ===== BƯỚC 7: Thuyết minh ngay trên khu Ghi chú =====
      {
        id: 'notes-explain',
        title: 'Ghi chú và Chatbot',
        text: this.makeText(
          'Ghi chú cho phép tạo, lưu trữ và quản lý các ghi chú cá nhân liên quan khi làm việc.<br/>' +
          'Chatbot cho phép trò chuyện, đặt câu hỏi, truy vấn về các vấn đề pháp luật với câu trả lời đầy đủ rõ ràng chi tiết .',
          7, total
        ),
        attachTo: { element: '.detail-work-space-nav-outlet', on: 'left' },
        buttons: [
          { text: 'Kết thúc', type: 'cancel', classes: 'tour-btn tour-btn--primary ml-auto' }
        ]
      }
    ];
  }

  private makeText(content: string, index: number, total: number) {
    return `
      <div class="tour-body">
        <div class="tour-content">
          <p>${content}</p>
        </div>
      </div>
    `;
  }


  @HostListener('window:resize', [])
  onResize() {
    this.checkScreenSize();
  }

  private checkScreenSize(): void {
    this.isMobile = window.innerWidth < 1200; // hoặc breakpoint bạn muốn
  }
  onTourCompleted() {
    console.log('Tour completed!');
    // Lưu trạng thái đã hoàn thành tour
    localStorage.setItem('isNewUser', 'false');
    this.isNewUser = false;
    this.toast.success('Đã hoàn thành hướng dẫn!', 'Thành công', {
      closeButton: true,
      positionClass: 'toast-top-right',
      toastClass: 'toast ngx-toastr',
    });
  }

  onTourSkipped() {
    console.log('Tour skipped!');
    // Có thể lưu trạng thái bỏ qua nếu muốn
    Swal.fire({
      title: 'Bạn có muốn xem lại hướng dẫn sau?',
      text: 'Bạn có thể xem lại hướng dẫn bất cứ lúc nào',
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#008fd3',
      cancelButtonColor: '#EEE',
      confirmButtonText: 'Không hiển thị nữa',
      cancelButtonText: 'Để sau',
    }).then((result) => {
      if (result.isConfirmed) {
        localStorage.setItem('isNewUser', 'false');
        this.isNewUser = false;
      }
    });
  }
  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    this._unsubscribeAll.next(null);
    this._unsubscribeAll.complete();
    this.changeData.changeData.next(false);
    this.cacheDataService.clearCache();
    this._destroy$.next();
    this._destroy$.complete();
  }

  openMobileSearchModal(modalTemplate: TemplateRef<any>) {
    this.modalService.open(modalTemplate, { centered: true });
  }

  onPageSizeChange(event: Event) {
    const input = event.target as HTMLInputElement;
    const newSize = parseInt(input.value, 10);
    if (!isNaN(newSize) && newSize > 0) {
      this.page_size = newSize;
      this.getAllWorkSpace(true, false);
    }
  }
}
