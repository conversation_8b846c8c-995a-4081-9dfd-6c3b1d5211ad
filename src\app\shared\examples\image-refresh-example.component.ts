import { Component, OnInit, OnDestroy } from '@angular/core';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ImageRefreshService } from '../services/image-refresh.service';

@Component({
  selector: 'app-image-refresh-example',
  template: `
    <div class="image-refresh-example">
      <h3>Image Refresh Service Example</h3>
      
      <div class="example-section">
        <h4>S3 Images with Auto Refresh</h4>
        <div class="image-grid">
          <div *ngFor="let imageUrl of s3Images; let i = index" class="image-item">
            <img 
              [src]="imageUrl" 
              [alt]="'S3 Image ' + (i + 1)"
              appAutoRefreshImage
              [refreshCallback]="refreshImages"
              [fallbackImage]="fallbackImage"
              (imageExpired)="onImageExpired($event)"
              (imageRefreshed)="onImageRefreshed($event)"
              class="example-image" />
            <p>Image {{ i + 1 }}</p>
          </div>
        </div>
      </div>

      <div class="example-section">
        <h4>Controls</h4>
        <button (click)="addTestImage()" class="btn btn-primary">Add Test Image</button>
        <button (click)="refreshImages()" class="btn btn-secondary">Refresh All Images</button>
        <button (click)="clearAllImages()" class="btn btn-danger">Clear All Images</button>
      </div>

      <div class="example-section">
        <h4>Status</h4>
        <div class="status-info">
          <p>Total registered images: {{ registeredImagesCount }}</p>
          <p>Expired images detected: {{ expiredImagesCount }}</p>
          <p>Last refresh: {{ lastRefreshTime || 'Never' }}</p>
        </div>
      </div>

      <div class="example-section">
        <h4>Logs</h4>
        <div class="logs">
          <div *ngFor="let log of logs" class="log-entry" [class]="log.type">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .image-refresh-example {
      padding: 20px;
      max-width: 800px;
      margin: 0 auto;
    }

    .example-section {
      margin-bottom: 30px;
      padding: 15px;
      border: 1px solid #ddd;
      border-radius: 8px;
    }

    .image-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 15px;
      margin-top: 15px;
    }

    .image-item {
      text-align: center;
    }

    .example-image {
      width: 100%;
      height: 150px;
      object-fit: cover;
      border-radius: 8px;
      border: 2px solid #ddd;
    }

    .btn {
      margin-right: 10px;
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }

    .btn-primary { background-color: #007bff; color: white; }
    .btn-secondary { background-color: #6c757d; color: white; }
    .btn-danger { background-color: #dc3545; color: white; }

    .status-info p {
      margin: 5px 0;
      font-weight: bold;
    }

    .logs {
      max-height: 200px;
      overflow-y: auto;
      background-color: #f8f9fa;
      padding: 10px;
      border-radius: 4px;
    }

    .log-entry {
      margin-bottom: 5px;
      font-family: monospace;
      font-size: 12px;
    }

    .log-entry.info { color: #007bff; }
    .log-entry.warning { color: #ffc107; }
    .log-entry.error { color: #dc3545; }
    .log-entry.success { color: #28a745; }

    .log-time {
      color: #6c757d;
      margin-right: 10px;
    }
  `]
})
export class ImageRefreshExampleComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  
  s3Images: string[] = [
    'https://example-bucket.s3.amazonaws.com/image1.jpg',
    'https://example-bucket.s3.amazonaws.com/image2.jpg',
    'https://example-bucket.s3.amazonaws.com/image3.jpg'
  ];
  
  fallbackImage = 'assets/images/pages/landing/cls-article.svg';
  registeredImagesCount = 0;
  expiredImagesCount = 0;
  lastRefreshTime: string | null = null;
  
  logs: Array<{time: string, message: string, type: string}> = [];

  constructor(private imageRefreshService: ImageRefreshService) {}

  ngOnInit(): void {
    this.setupImageRefreshService();
    this.registerInitialImages();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.imageRefreshService.clearAll();
  }

  private setupImageRefreshService(): void {
    this.imageRefreshService.configure({
      checkInterval: 30000, // 30 seconds for demo
      retryAttempts: 2,
      retryDelay: 1000
    });

    this.imageRefreshService.getExpiredImages()
      .pipe(takeUntil(this.destroy$))
      .subscribe(expiredUrls => {
        this.expiredImagesCount += expiredUrls.length;
        this.addLog(`Detected ${expiredUrls.length} expired images`, 'warning');
        this.refreshImages();
      });
  }

  private registerInitialImages(): void {
    this.s3Images.forEach(url => {
      this.imageRefreshService.registerImage(url);
    });
    this.registeredImagesCount = this.s3Images.length;
    this.addLog(`Registered ${this.s3Images.length} images for monitoring`, 'info');
  }

  refreshImages = (): void => {
    this.lastRefreshTime = new Date().toLocaleTimeString();
    this.addLog('Refreshing all images...', 'info');
    
    // Simulate API call to get fresh URLs
    setTimeout(() => {
      this.addLog('Images refreshed successfully', 'success');
    }, 1000);
  }

  addTestImage(): void {
    const newImageUrl = `https://example-bucket.s3.amazonaws.com/image${Date.now()}.jpg`;
    this.s3Images.push(newImageUrl);
    this.imageRefreshService.registerImage(newImageUrl);
    this.registeredImagesCount++;
    this.addLog(`Added new test image: ${newImageUrl}`, 'info');
  }

  clearAllImages(): void {
    this.s3Images = [];
    this.imageRefreshService.clearAll();
    this.registeredImagesCount = 0;
    this.expiredImagesCount = 0;
    this.addLog('Cleared all images', 'info');
  }

  onImageExpired(url: string): void {
    this.addLog(`Image expired: ${url}`, 'error');
  }

  onImageRefreshed(url: string): void {
    this.addLog(`Image refreshed: ${url}`, 'success');
  }

  private addLog(message: string, type: string): void {
    const time = new Date().toLocaleTimeString();
    this.logs.unshift({ time, message, type });
    
    // Keep only last 20 logs
    if (this.logs.length > 20) {
      this.logs = this.logs.slice(0, 20);
    }
  }
}
