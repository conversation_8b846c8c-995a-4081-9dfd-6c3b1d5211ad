import { Injectable } from '@angular/core';
import {
  GraphApiResponse,
  ApiNode,
  ApiRelationship,
  ExpansionHistory,
  GraphFilterOptions,
} from '../types/graph.types';
import {
  getNodeIdFromApiNode,
  generateRelationshipKey,
  safeArray,
} from '../helper/helper';
import { GraphCacheService } from './graph-cache.service';

/**
 * Service for managing node expansion and collapse operations
 */
@Injectable({
  providedIn: 'root',
})
export class GraphExpansionService {
  private expansionHistory: Map<string, ExpansionHistory> = new Map();

  constructor(private cacheService: GraphCacheService) {}

  /**
   * Merge new graph data into existing graph data
   */
  mergeGraphData(
    existingData: GraphApiResponse,
    newData: GraphApiResponse,
    seedNodeId: string
  ): {
    mergedData: GraphApiResponse;
    newNodes: ApiNode[];
    newRelationships: ApiRelationship[];
  } {
    if (!existingData || !newData) {
      return {
        mergedData: newData || existingData,
        newNodes: [],
        newRelationships: [],
      };
    }

    // Merge nodes - deduplicate by ID
    const existingNodeIds = new Set(
      safeArray(existingData.nodes).map((n: ApiNode) =>
        getNodeIdFromApiNode(n)
      )
    );

    const newNodes = safeArray(newData.nodes).filter((n: ApiNode) => {
      const nodeId = getNodeIdFromApiNode(n);
      return nodeId && !existingNodeIds.has(nodeId);
    });

    const mergedNodes = [...safeArray(existingData.nodes), ...newNodes];

    // Merge relationships - deduplicate using relationship key
    const existingRelKeys = new Set(
      safeArray(existingData.relationships).map((r: ApiRelationship) =>
        generateRelationshipKey(r)
      )
    );

    const newRelationships = safeArray(newData.relationships).filter(
      (r: ApiRelationship) => {
        const relKey = generateRelationshipKey(r);
        return !existingRelKeys.has(relKey);
      }
    );

    const mergedRelationships = [
      ...safeArray(existingData.relationships),
      ...newRelationships,
    ];

    // Preserve original seed_node_ids - do not merge with new data's seed_node_ids
    // The original seed_node_ids represent the root/initial documents, which should remain unchanged
    const mergedSeedIds = safeArray(existingData.seed_node_ids);

    // Merge dynamic filter options (union of option sets from existing and new data)
    const mergedFilterOptions: GraphFilterOptions | undefined = (() => {
      const existingFilters = existingData.filter_options || {};
      const newFilters = newData.filter_options || {};

      // If neither side has dynamic filters, don't add the property
      if (
        !existingFilters.relationship_types &&
        !existingFilters.co_quan_ban_hanh &&
        !existingFilters.tinh_trang_hieu_luc &&
        !existingFilters.loai_van_ban &&
        !newFilters.relationship_types &&
        !newFilters.co_quan_ban_hanh &&
        !newFilters.tinh_trang_hieu_luc &&
        !newFilters.loai_van_ban
      ) {
        return existingData.filter_options;
      }

      const mergeStringArrays = (
        a?: string[],
        b?: string[]
      ): string[] | undefined => {
        const combined = [...(a || []), ...(b || [])];
        if (!combined.length) return undefined;
        return Array.from(new Set(combined));
      };

      return {
        relationship_types: mergeStringArrays(
          existingFilters.relationship_types,
          newFilters.relationship_types
        ),
        co_quan_ban_hanh: mergeStringArrays(
          existingFilters.co_quan_ban_hanh,
          newFilters.co_quan_ban_hanh
        ),
        tinh_trang_hieu_luc: mergeStringArrays(
          existingFilters.tinh_trang_hieu_luc,
          newFilters.tinh_trang_hieu_luc
        ),
        loai_van_ban: mergeStringArrays(
          existingFilters.loai_van_ban,
          newFilters.loai_van_ban
        ),
      };
    })();

    const mergedData: GraphApiResponse = {
      ...existingData,
      nodes: mergedNodes,
      relationships: mergedRelationships,
      seed_node_ids: mergedSeedIds,
      total_nodes: mergedNodes.length,
      total_relationships: mergedRelationships.length,
      filter_options: mergedFilterOptions,
    };

    // Track this expansion - only track nodes and relationships that were actually added
    this.trackExpansion(seedNodeId, newNodes, newRelationships);

    return {
      mergedData,
      newNodes,
      newRelationships,
    };
  }

  /**
   * Track which nodes and relationships came from this expansion
   */
  private trackExpansion(
    seedNodeId: string,
    newNodes: ApiNode[],
    newRelationships: ApiRelationship[]
  ): void {
    const nodeIds = new Set<string>();
    const relationshipKeys = new Set<string>();

    // Track all new nodes from this expansion (except the seed node itself)
    newNodes.forEach((node: ApiNode) => {
      const nodeId = getNodeIdFromApiNode(node);
      if (nodeId && nodeId !== seedNodeId) {
        nodeIds.add(nodeId);
      }
    });

    // Track all new relationships from this expansion
    newRelationships.forEach((rel: ApiRelationship) => {
      relationshipKeys.add(generateRelationshipKey(rel));
    });

    // Store in expansion history
    this.expansionHistory.set(seedNodeId, { nodeIds, relationshipKeys });
  }

  /**
   * Remove nodes and relationships from a previous expansion of a node
   */
  removePreviousExpansion(
    graphData: GraphApiResponse,
    seedNodeId: string,
    rootNodeId: string,
    hiddenNodeIds: Set<string>
  ): {
    updatedData: GraphApiResponse;
    removedNodeIds: Set<string>;
  } {
    const previousExpansion = this.expansionHistory.get(seedNodeId);
    if (!previousExpansion) {
      return { updatedData: graphData, removedNodeIds: new Set() };
    }

    // Get initial seed node IDs (root and original seeds) - these should never be removed
    const protectedNodeIds = this.cacheService.getProtectedNodeIds(
      graphData,
      rootNodeId
    );

    // Find nodes that should be removed
    const nodesToRemove = new Set<string>();
    previousExpansion.nodeIds.forEach((nodeId: string) => {
      if (!protectedNodeIds.has(nodeId)) {
        const isOnlyConnectedViaThisExpansion =
          this.isNodeOnlyConnectedViaExpansion(
            graphData,
            nodeId,
            seedNodeId,
            previousExpansion.relationshipKeys
          );
        if (isOnlyConnectedViaThisExpansion) {
          nodesToRemove.add(nodeId);
        }
      }
    });

    // Remove nodes
    let updatedNodes = safeArray(graphData.nodes);
    if (nodesToRemove.size > 0) {
      updatedNodes = updatedNodes.filter((node: ApiNode) => {
        const nodeId = getNodeIdFromApiNode(node);
        return !nodesToRemove.has(nodeId);
      });
      nodesToRemove.forEach((nodeId) => hiddenNodeIds.delete(nodeId));
    }

    // Remove relationships that were part of this expansion
    let updatedRelationships = safeArray(graphData.relationships);
    if (previousExpansion.relationshipKeys.size > 0) {
      updatedRelationships = updatedRelationships.filter(
        (rel: ApiRelationship) => {
          return !previousExpansion.relationshipKeys.has(
            generateRelationshipKey(rel)
          );
        }
      );
    }

    // Also remove relationships that involve removed nodes
    if (nodesToRemove.size > 0) {
      updatedRelationships = updatedRelationships.filter(
        (rel: ApiRelationship) => {
          const sourceId = String(rel.source_id);
          const targetId = String(rel.target_id);
          return !nodesToRemove.has(sourceId) && !nodesToRemove.has(targetId);
        }
      );
    }

    const updatedData: GraphApiResponse = {
      ...graphData,
      nodes: updatedNodes,
      relationships: updatedRelationships,
      total_nodes: updatedNodes.length,
      total_relationships: updatedRelationships.length,
    };

    // Clear the expansion history entry (will be updated with new expansion)
    this.expansionHistory.delete(seedNodeId);

    // Invalidate caches
    this.cacheService.invalidateCaches();

    return {
      updatedData,
      removedNodeIds: nodesToRemove,
    };
  }

  /**
   * Check if a node is only connected via relationships from a specific expansion
   */
  private isNodeOnlyConnectedViaExpansion(
    graphData: GraphApiResponse,
    nodeId: string,
    seedNodeId: string,
    expansionRelationshipKeys: Set<string>
  ): boolean {
    const allRelationships = safeArray(graphData.relationships);

    // Find all relationships involving this node
    const nodeRelationships = allRelationships.filter(
      (rel: ApiRelationship) => {
        const sourceId = String(rel.source_id);
        const targetId = String(rel.target_id);
        return sourceId === nodeId || targetId === nodeId;
      }
    );

    // Check if all relationships involving this node are from this expansion
    for (const rel of nodeRelationships) {
      if (!expansionRelationshipKeys.has(generateRelationshipKey(rel))) {
        // This node has a relationship outside of this expansion, don't remove it
        return false;
      }
    }

    return true;
  }

  /**
   * Get descendant node IDs for a given node
   */
  getDescendantNodeIds(
    nodeId: string,
    graphData: GraphApiResponse | null,
    rootNodeId: string,
    hiddenNodeIds: Set<string>
  ): Set<string> {
    if (!graphData) return new Set();

    const neighbors = this.cacheService.buildUndirectedNeighborMap(graphData);
    const protectedNodes = this.cacheService.getProtectedNodeIds(
      graphData,
      rootNodeId
    );
    const visited = new Set<string>();
    const stack: string[] = [];

    const direct = neighbors.get(nodeId);
    if (direct) {
      direct.forEach((n) => {
        if (!hiddenNodeIds.has(n)) {
          stack.push(n);
        }
      });
    }

    while (stack.length > 0) {
      const current = stack.pop()!;
      if (visited.has(current)) {
        continue;
      }

      if (protectedNodes.has(current)) {
        // Do not hide or traverse beyond protected nodes (root, seed nodes)
        continue;
      }

      visited.add(current);
      const next = neighbors.get(current);
      if (next) {
        next.forEach((n) => {
          if (!visited.has(n) && !hiddenNodeIds.has(n)) {
            stack.push(n);
          }
        });
      }
    }

    return visited;
  }

  /**
   * Get hidden descendant node IDs for a given node
   */
  getHiddenDescendantsForNode(
    nodeId: string,
    graphData: GraphApiResponse | null,
    rootNodeId: string,
    hiddenNodeIds: Set<string>
  ): Set<string> {
    const hiddenNodes = new Set<string>();
    if (hiddenNodeIds.size === 0) {
      return hiddenNodes;
    }

    if (nodeId === rootNodeId) {
      hiddenNodeIds.forEach((id) => hiddenNodes.add(id));
      return hiddenNodes;
    }

    const neighbors = this.cacheService.buildUndirectedNeighborMap(graphData);
    const visited = new Set<string>();
    const stack: string[] = [];
    const direct = neighbors.get(nodeId);
    if (direct) {
      direct.forEach((n) => stack.push(n));
    }

    while (stack.length > 0) {
      const current = stack.pop()!;
      if (visited.has(current)) {
        continue;
      }
      visited.add(current);

      if (hiddenNodeIds.has(current)) {
        hiddenNodes.add(current);
      }

      const next = neighbors.get(current);
      if (next) {
        next.forEach((n) => {
          if (!visited.has(n)) {
            stack.push(n);
          }
        });
      }
    }

    return hiddenNodes;
  }

  /**
   * Clear expansion history
   */
  clearExpansionHistory(): void {
    this.expansionHistory.clear();
  }

  /**
   * Check if a node has been expanded before
   */
  hasExpansionHistory(seedNodeId: string): boolean {
    return this.expansionHistory.has(seedNodeId);
  }
}

