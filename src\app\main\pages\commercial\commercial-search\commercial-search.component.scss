// Variables
$bg-slate-50: #f8fafc;
$bg-white: #ffffff;
$border-gray-200: #e2e8f0;
$text-gray-900: #0f172a;
$text-gray-600: #475569;
$text-gray-500: #64748b;
$blue-600: #008fe3;
$blue-700: #1d4ed8;
$green-600: #16a34a;
$red-600: #dc2626;
$primary-color: #0056b3;
$border-color: #eee;
$sidebar-bg: #fff;
$text-header: #333;
$text-body: #555;

.legal-search-layout {
  display: flex;
  height: 100%;
  background-color: $bg-slate-50;
  font-family: 'Inter', sans-serif;
  overflow: hidden;
}

.sidebar-filter {
  background-color: $sidebar-bg;
  padding: 20px 0 20px 15px;
  border-right: 1px solid $border-color;
  height: 100%;
  min-width: 280px;
  display: flex; 
  flex-direction: column;
  .sidebar-header {
    padding-right: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 25px;
    flex-shrink: 0;
    h3 {
      font-size: 1.1rem;
      font-weight: 700;
      margin: 0;
      color: #000;
    }
    
    i {
      width: 20px;
      height: 20px;
    }
  }
  .divider {
    height: 1px;
    background-color: $border-color;
    margin: 15px;
  }

  .sidebar-content {
    flex: 1;
    overflow-y: overlay;
    overflow-x: hidden;
    // padding: 20px;
    padding-right: 10px;
    min-height: 0;
    &::-webkit-scrollbar {
      width: 6px; 
      background-color: transparent;
    }
    &::-webkit-scrollbar-thumb {
      background-color: transparent; 
      border-radius: 10px;
      transition: background-color 0.3s;
    }

    &:hover {
      &::-webkit-scrollbar-thumb {
        background-color: #cbd5e1; 
      }
    }
      &::-webkit-scrollbar-thumb:active {
      background-color: #94a3b8; 
    }
  }

  .filter-group {
    
    // 1. Header (Clickable)
    .filter-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: pointer;
      padding: 8px 0;
      user-select: none;

      h4 {
        font-size: 0.85rem;
        font-weight: 700;
        margin: 0;
        color: #444;
        text-transform: uppercase;
      }

      .arrow-icon {
        width: 16px;
        height: 16px;
        color: #888;
        transition: transform 0.3s ease;
      }

      &:hover h4 {
        color: $primary-color;
      }
    }

    // 2. Body Animation Wrapper
    .filter-body-wrapper {
      display: grid;
      grid-template-rows: 0fr;
      transition: grid-template-rows 0.3s ease-out;
      
      .filter-body {
        overflow: hidden;
        min-height: 0;
      }
    }

    // --- TRẠNG THÁI MỞ ---
    &.open {
      .arrow-icon {
        transform: rotate(180deg); // Xoay mũi tên lên
        color: $primary-color;
      }

      .filter-body-wrapper {
        grid-template-rows: 1fr;
      }
      
      // Margin bottom khi mở để thoáng
      .filter-body {
          padding-bottom: 5px;
      }
    }
  }
  .scope-toggle {
    display: flex;
    background: #f2f4f8;
    border-radius: 6px;
    padding: 3px;
    margin-top: 5px;

    button {
      flex: 1;
      border: none;
      background: transparent;
      padding: 6px 0;
      font-size: 0.85rem;
      font-weight: 500;
      color: #666;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.2s;

      &.active {
        background: #fff;
        color: $primary-color;
        font-weight: 600;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
      }
    }
  }
  .collapsible-list {
    max-height: 192px; 
    overflow: hidden;
    transition: max-height 0.5s ease-in-out;
    &.expanded {
      max-height: 800px;
    }
  }
  .checkbox-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 10px;
    .expand-action {
      margin-top: 10px;
      width: 100%;              // Chiếm toàn bộ chiều rộng
      display: flex;            // Sử dụng Flexbox
      justify-content: center;  // Căn giữa theo chiều ngang
      
      .btn-expand {
        background: #f1f5f9;
        border: none;
        padding: 6px 12px;      // Padding để nút trông đầy đặn hơn
        border-radius: 16px;    // Bo tròn nút
        color: #0056b3;
        font-size: 0.8rem;
        font-weight: 600;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 6px;
        transition: all 0.2s ease;

        i, svg {
          width: 14px;
          height: 14px;
          transition: transform 0.3s ease; // Hiệu ứng xoay mượt mà
        }

        &:hover {
          background-color: #e2e8f0;
          color: #004494;
        }

        // --- LOGIC XOAY MŨI TÊN ---
        // Khi có class .expanded, icon bên trong sẽ xoay 180 độ (thành hướng lên)
        &.expanded {
          i, svg {
            transform: rotate(180deg);
          }
        }
      }
    }
    
    .custom-checkbox-row {
      display: flex;
      align-items: flex-start;
      cursor: pointer;
      font-size: 0.9rem;
      color: $text-body;
      position: relative;
      padding-left: 28px;
      
      input {
        position: absolute;
        opacity: 0;
        cursor: pointer;
        height: 0;
        width: 0;

        &:checked ~ .checkmark {
          background-color: $primary-color;
          border-color: $blue-600;
          
          &:after {
            display: block;
          }
        }
      }

      .checkmark {
        position: absolute;
        top: 2px;
        left: 0;
        height: 18px;
        width: 18px;
        background-color: #fff;
        border: 1px solid #ccc;
        border-radius: 3px;
        transition: all 0.2s;

        &:after {
          content: "";
          position: absolute;
          display: none;
          left: 5px;
          top: 2px;
          width: 6px;
          height: 10px;
          border: solid white;
          border-width: 0 2px 2px 0;
          transform: rotate(45deg);
        }
      }

      .label-text {
          flex: 1;
          line-height: 1.4;
      }

      .count-text {
        color: #999;
        font-size: 0.8rem;
        margin-left: 4px;
      }

      &:hover {
        color: $primary-color;
        .checkmark {
            border-color: $primary-color;
        }
      }
    }
  }

.expand-action {
  margin-top: 12px;
  width: 100%;
  display: flex;
  justify-content: center; // Căn giữa nút theo chiều ngang
  
  .btn-expand {
    background: #f1f5f9;    // Màu nền nhẹ
    border: none;
    padding: 8px 20px;      // Độ rộng nút
    border-radius: 20px;    // Bo tròn nút
    color: #0056b3;
    font-size: 0.85rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    text-align: center;

    &:hover {
      background-color: #e2e8f0;
      color: #004494;
    }
  }
}

  .custom-select {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      color: #555;
      background-color: #fafafa;
      margin-top: 5px;
      font-size: 0.9rem;
      
      &:focus {
          outline: none;
          border-color: $primary-color;
      }
  }

  // .divider { height: 1px; background: $border-gray-200; margin: 8px 0; }

  .filter-list {
    display: flex; flex-direction: column; gap: 10px; padding-bottom: 12px; max-height: 250px; overflow-y: auto;
    &::-webkit-scrollbar { width: 4px; }
    &::-webkit-scrollbar-thumb { background: #cbd5e1; border-radius: 4px; }
    .filter-checkbox {
      display: flex; align-items: center; gap: 10px; font-size: 14px; color: $text-gray-600; cursor: pointer;
      &:hover { color: $text-gray-900; }
      input { width: 16px; height: 16px; border-radius: 4px; border: 1px solid #cbd5e1; &:checked { accent-color: $blue-600; } }
      .count { font-size: 12px; color: $text-gray-500; }
    }
  }
}

/* --- MAIN CONTENT --- */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.search-header {
  background: $bg-white;
  padding: 24px 32px;
  border-bottom: 1px solid $border-gray-200;
  z-index: 10;

  .page-title {
    font-size: 24px;
    font-weight: 700;
    color: $text-gray-900;
    margin-bottom: 20px;
  }

  .search-bar-container {
    display: flex;
    align-items: stretch; // Kéo dãn chiều cao bằng nhau
    gap: 12px;
    max-width: 800px;
    height: 44px;

    // Style cho ô input group
    .search-input-group {
      flex: 1;
      display: flex;
      align-items: center;
      background: #f8fafc;
      border: 1px solid $border-gray-200;
      border-radius: 10px;
      padding: 0 12px;
      transition: all 0.2s ease;

      // Hiệu ứng focus toàn bộ khung
      &:focus-within {
        background: $bg-white;
        border-color: $blue-600;
        box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.15);
      }

      .search-icon-wrapper {
        display: flex;
        align-items: center;
        color: #94a3b8;
        margin-right: 10px;
        
        i { width: 18px; height: 18px; }
      }

      input {
        flex: 1;
        border: none;
        background: transparent;
        outline: none;
        font-size: 14px;
        color: $text-gray-900;
        height: 100%;
        width: 100%;

        &::placeholder {
          color: #94a3b8;
        }
      }

      .btn-clear {
        border: none;
        background: transparent;
        color: #cbd5e1;
        padding: 4px;
        cursor: pointer;
        display: flex;
        align-items: center;
        &:hover { color: $text-gray-600; }
        i { width: 16px; height: 16px; }
      }
    }

    .btn-search-primary {
      padding: 0 24px;
      background: $blue-600;
      color: white;
      border: none;
      border-radius: 10px;
      font-weight: 600;
      font-size: 14px;
      cursor: pointer;
      transition: background 0.2s;
      white-space: nowrap;

      &:hover { background: $blue-700; }
      &:active { transform: translateY(1px); }
    }
  }
}

/* --- TRẠNG THÁI HIỂN THỊ --- */
.state-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 60px 20px;
  height: 60vh; // Căn giữa màn hình

  .state-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    
    i { width: 36px; height: 36px; }
  }

  .state-title {
    font-size: 18px;
    font-weight: 700;
    color: $text-gray-900;
    margin-bottom: 8px;
  }

  .state-desc {
    font-size: 14px;
    color: $text-gray-500;
    max-width: 450px;
    line-height: 1.5;
  }
}

// Spinner bootstrap custom size
.spinner-border {
  width: 2.5rem;
  height: 2.5rem;
  border-width: 0.25em;
  color: $blue-600;
}

/* --- RESULTS INFO --- */
.results-info {
  margin-bottom: 16px;
  font-size: 14px;
  color: $text-gray-600;
  display: flex;
  align-items: center;
  gap: 8px;

  strong { color: $text-gray-900; }
  .page-info { color: $text-gray-500; font-size: 13px; }
}

.results-area {
  flex: 1;
  overflow-y: auto;
  padding: 24px 32px;
  background: rgba(248, 250, 252, 0.5);

  .results-info {
    margin-bottom: 16px;
    font-size: 14px;
    color: $text-gray-600;
    strong { color: $text-gray-900; }
  }
}

/* --- EMPTY STATE & LOADING --- */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  text-align: center;
  
  .icon-circle {
    width: 80px;
    height: 80px;
    background: #f1f5f9;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 24px;
    color: #94a3b8;
    
    // Style cho trạng thái Welcome
    &.highlight {
      background: #eff6ff; // Xanh nhạt
      color: $blue-600;
    }

    i { width: 36px; height: 36px; }
  }
  
  h3 { 
    font-size: 18px; 
    font-weight: 700; 
    color: $text-gray-900; 
    margin-bottom: 8px;
  }
  
  p { 
    font-size: 14px; 
    color: $text-gray-500; 
    max-width: 400px;
    line-height: 1.5;
  }
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

/* --- CARDS LIST ---  */
.cards-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.result-card {
  background: $bg-white;
  border: 1px solid $border-gray-200;
  border-radius: 12px;
  padding: 24px;
  transition: all 0.2s;
  cursor: pointer;

  &:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    border-color: #93c5fd;
    transform: translateY(-2px);
  }

  .card-header {
    display: flex; justify-content: space-between; margin-bottom: 8px;
    .doc-title {
      font-size: 18px; font-weight: 700; color: $blue-700; margin: 0; line-height: 1.4;
      .index { color: $text-gray-900; font-size: 16px; margin-right: 8px; }
    }
    .copy-icon { width: 14px; height: 14px; color: #94a3b8; &:hover { color: $blue-600; } }
  }

  .card-body {
    padding-left: 38px;
    .snippet {
      font-size: 14px; color: $text-gray-600; font-style: italic; line-height: 1.6; margin-bottom: 12px;
      .read-more { color: $blue-600; font-weight: 600; font-style: normal; font-size: 12px; cursor: pointer; &:hover { text-decoration: underline; } }
    }
    .metadata-chips {
      display: flex; gap: 16px; font-size: 12px; color: $text-gray-500;
      .chip { display: flex; align-items: center; gap: 4px; strong { color: $text-gray-900; } &.status-active strong { color: $green-600; } }
    }
  }

  .card-footer {
    margin-top: 16px; padding-top: 12px; border-top: 1px solid #f1f5f9; padding-left: 38px; display: flex; gap: 12px; font-size: 12px; color: $text-gray-500;
    .action-link { cursor: pointer; transition: color 0.2s; &:hover { color: $blue-600; } &.highlight { font-weight: 600; color: $text-gray-900; &:hover { color: $blue-600; } } }
    .separator { color: #cbd5e1; }
  }
}

/* --- PAGINATION --- */
.pagination-wrapper {
  margin-top: 24px; display: flex; justify-content: center; gap: 8px;
  .btn-page {
    width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; border-radius: 8px; border: 1px solid $border-gray-200; background: $bg-white; color: $text-gray-600; font-size: 12px; font-weight: 500; transition: all 0.2s;
    &:hover:not(:disabled) { background: #f1f5f9; }
    &.active { background: $blue-600; color: white; border-color: $blue-600; }
    &:disabled { opacity: 0.5; cursor: not-allowed; }
    i { width: 14px; height: 14px; }
  }
}

// Responsive
@media (max-width: 991px) {
  .sidebar-filter { display: none; } 
  .search-header { padding: 16px; }
  .results-area { padding: 16px; }
}

// Variables màu sắc dựa trên ảnh
$primary-blue: #0056b3; // Màu xanh đậm của tiêu đề
$text-dark: #333;
$text-muted: #666;
$border-color: #e0e0e0;
$bg-hover: #f9f9f9;

.results-info {
  font-size: 0.9rem;
  margin-bottom: 15px;
}

.cards-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.law-card {
  background: #fff;
  border: 1px solid $border-color;
  border-radius: 16px;
  padding: 12px 20px 0 20px;
  transition: all 0.2s ease;
  position: relative;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border-color: #b3d7ff;
    background-color: #fff;
  }

  // 1. Header Styles
  .law-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 4px;

    .card-title-wrapper {
      display: flex;
      align-items: center; // Căn giữa icon và text
      gap: 8px;            // Khoảng cách giữa text và icon copy
      flex: 1;             // Chiếm hết chiều rộng còn lại
    }
    .card-title {
      font-size: 1.1rem;
      font-weight: 700;
      margin: 0;
      line-height: 1.4;
      cursor: pointer;
      display: flex;
      gap: 8px;

      .index-number {
        color: #000;
        margin-right: 8px;
        min-width: 25px;
        display: inline-block;
      }
      &:hover .title-text {
        text-decoration: underline;
      }
      .title-text {
        color: $blue-600; // Tiêu đề màu xanh
        &:hover {
          text-decoration: underline;
        }
      }
    }

    .copy-icon {
      width: 16px;
      height: 16px;
      color: #999;
      cursor: pointer;
      &:hover {
        color: $primary-blue;
      }
    }
  }

  .copy-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    transition: all 0.2s;
    border-radius: 4px;

    i {
      width: 14px;  // Kích thước icon nhỏ gọn
      height: 14px;
    }

    // Hiệu ứng Hover
    &:hover {
      background-color: #e3f2fd; // Nền xanh nhạt khi hover
      color: $primary-blue;      // Icon chuyển xanh đậm
      transform: scale(1.1);     // Phóng to nhẹ
    }
    
    &:active {
      transform: scale(0.95);    // Nhấn xuống
    }
  }
  // 2. Body Styles (Snippet)
  .law-card-body {
    margin-bottom: 6px;
    padding-left: 33px;

    .snippet-wrapper {
      font-size: 0.9rem;
      line-height: 1.4;
      color: #333;

      .snippet-content {
        display: inline;
        
        // Mặc định (khi chưa expanded): Giới hạn 2 dòng
        &:not(.expanded) {
          display: -webkit-box;
          -webkit-line-clamp: 2; // Số dòng muốn hiển thị tối đa
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        // CSS cho thẻ highlight từ backend trả về
        ::ng-deep mark {
          background-color: #ffeb3b;
          color: black;
          padding: 0 2px;
          border-radius: 2px;
        }
      }

      .read-more-btn {
        display: inline-block;
        margin-left: 5px;
        color: #0056b3; // Màu xanh blue
        font-weight: 500;
        font-style: italic;
        text-decoration: none;
        font-size: 0.85rem;
        cursor: pointer;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }

  // 3. Meta Data Styles
  .law-card-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    padding-left: 33px;
    // margin-bottom: 8px;
    font-size: 0.8rem;

    .meta-item {
      display: flex;
      gap: 4px;
      align-items: center;

      .label {
        color: $text-muted;
      }

      .value {
        color: #000;
        &.text-success { color: #28a745 !important; } // Xanh lá
        &.text-danger { color: #dc3545 !important; } // Đỏ
        &.text-warning { color: #ffc107 !important; }
      }
    }
  }

  // 4. Footer Actions
  .law-card-footer {
    padding-left: 33px;
    border-top: 1px dash #eee;
    display: flex;
    align-items: center;
    gap: 8px;

    .btn-text {
      background: none;
      border: none;
      padding: 2px 4px;
      font-size: 0.85rem;
      color: #555;
      font-weight: 500;
      cursor: pointer;
      &:hover {
         background-color: #f5f5f5;
         border-radius: 4px;
      }
      &:hover, &:first-child {
        color: $primary-blue;
        font-weight: 600;
      }
    }

    .divider {
      color: #ccc;
      font-size: 0.8rem;
    }
  }
}

// Responsive cho mobile
@media (max-width: 768px) {
  .law-card-body, .law-card-meta, .law-card-footer {
    padding-left: 0; // Reset padding trên mobile
  }
  .law-card-meta {
    flex-direction: column;
    gap: 5px;
  }
}