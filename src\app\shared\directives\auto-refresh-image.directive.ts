import { 
  Directive, 
  ElementRef, 
  Input, 
  OnInit, 
  OnDestroy, 
  Output, 
  EventEmitter,
  Renderer2 
} from '@angular/core';
import { Subject } from 'rxjs';
import { takeUntil, debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { ImageRefreshService } from '../services/image-refresh.service';

@Directive({
  selector: 'img[appAutoRefreshImage]'
})
export class AutoRefreshImageDirective implements OnInit, OnDestroy {
  @Input() refreshCallback?: () => void;
  @Input() fallbackImage?: string;
  @Input() enableAutoRefresh = true;
  @Output() imageExpired = new EventEmitter<string>();
  @Output() imageRefreshed = new EventEmitter<string>();

  private destroy$ = new Subject<void>();
  private currentSrc = '';
  private retryCount = 0;
  private maxRetries = 3;
  private retryDelay = 2000; // 2 seconds

  constructor(
    private el: ElementRef<HTMLImageElement>,
    private renderer: Renderer2,
    private imageRefreshService: ImageRefreshService
  ) {}

  ngOnInit(): void {
    if (!this.enableAutoRefresh) {
      return;
    }

    this.setupImageMonitoring();
    this.listenForExpiredImages();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    
    if (this.currentSrc) {
      this.imageRefreshService.unregisterImage(this.currentSrc);
    }
  }

  private setupImageMonitoring(): void {
    const img = this.el.nativeElement;
    
    // Monitor src changes
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'src') {
          this.handleSrcChange();
        }
      });
    });

    observer.observe(img, {
      attributes: true,
      attributeFilter: ['src']
    });

    // Initial setup
    this.handleSrcChange();

    // Setup error handling
    this.renderer.listen(img, 'error', () => {
      this.handleImageError();
    });

    // Setup load success
    this.renderer.listen(img, 'load', () => {
      this.retryCount = 0;
    });
  }

  private handleSrcChange(): void {
    const img = this.el.nativeElement;
    const newSrc = img.src;

    if (this.currentSrc && this.currentSrc !== newSrc) {
      this.imageRefreshService.unregisterImage(this.currentSrc);
    }

    this.currentSrc = newSrc;
    
    if (newSrc && this.isS3Url(newSrc)) {
      this.imageRefreshService.registerImage(newSrc);
    }
  }

  private listenForExpiredImages(): void {
    this.imageRefreshService.getExpiredImages()
      .pipe(
        takeUntil(this.destroy$),
        debounceTime(1000),
        distinctUntilChanged()
      )
      .subscribe(expiredUrls => {
        if (expiredUrls.includes(this.currentSrc)) {
          this.handleExpiredImage();
        }
      });
  }

  private handleExpiredImage(): void {
    this.imageExpired.emit(this.currentSrc);
    
    if (this.refreshCallback) {
      this.refreshCallback();
    } else {
      this.retryLoadImage();
    }
  }

  private handleImageError(): void {
    if (this.retryCount < this.maxRetries) {
      this.retryCount++;
      setTimeout(() => {
        this.retryLoadImage();
      }, this.retryDelay * this.retryCount);
    } else {
      this.useFallbackImage();
    }
  }

  private retryLoadImage(): void {
    const img = this.el.nativeElement;
    const originalSrc = img.src;
    
    // Add timestamp to bypass cache
    const separator = originalSrc.includes('?') ? '&' : '?';
    const newSrc = `${originalSrc}${separator}_t=${Date.now()}`;
    
    img.src = newSrc;
    this.imageRefreshed.emit(newSrc);
  }

  private useFallbackImage(): void {
    if (this.fallbackImage) {
      const img = this.el.nativeElement;
      img.src = this.fallbackImage;
    }
  }

  private isS3Url(url: string): boolean {
    return url.includes('amazonaws.com') || 
           url.includes('s3.') || 
           url.includes('.s3.');
  }
}
